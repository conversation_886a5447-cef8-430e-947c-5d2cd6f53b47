# Generated by Django 5.1.7 on 2025-06-13 00:37

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("business", "0007_business_status_apiconfig"),
    ]

    operations = [
        migrations.CreateModel(
            name="VenderFee",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "vender",
                    models.CharField(
                        choices=[
                            ("Shago", "Shago"),
                            ("Sonite", "Sonite"),
                            ("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"),
                            ("<PERSON><PERSON>", "<PERSON>lo"),
                            ("YouVerify", "YouVerify"),
                            ("Blusalt", "Blusalt"),
                            ("Access", "Access"),
                            ("Jamb", "Jamb"),
                            ("EasyPayWema", "EasyPayWema"),
                            ("EasyPayZenith", "EasyPayZenith"),
                            ("EasyPayFidelity", "EasyPayFidelity"),
                            ("EasyPayEcobank", "EasyPayEcobank"),
                            ("MamaAfrica", "MamaAfrica"),
                            ("Dojah", "Dojah"),
                            ("Wema", "Wema"),
                            ("Kolomoni", "Kolomoni"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                (
                    "product",
                    models.CharField(
                        choices=[
                            ("Transfer", "Transfer"),
                            ("Airtime", "Airtime"),
                            ("Data", "Data"),
                            ("Kyc", "Kyc"),
                            ("VirtualAccount", "VirtualAccount"),
                            ("RecurringDebit", "RecurringDebit"),
                            ("CableTv", "CableTv"),
                            ("Epin", "Epin"),
                            ("Education", "Education"),
                            ("Electricity", "Electricity"),
                            ("Betting", "Betting"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("Mtn", "Mtn"),
                            ("Glo", "Glo"),
                            ("Airtel", "Airtel"),
                            ("9Mobile", "9Mobile"),
                            ("Dstv", "Dstv"),
                            ("Gotv", "Gotv"),
                            ("Startimes", "Startimes"),
                            ("AbujaElectric", "AbujaElectric"),
                            ("BeninElectric", "BeninElectric"),
                            ("EnuguElectric", "EnuguElectric"),
                            ("EkoElectric", "EkoElectric"),
                            ("IbadanElectric", "IbadanElectric"),
                            ("IkejaElectric", "IkejaElectric"),
                            ("JosElectric", "JosElectric"),
                            ("PortharcourtElectric", "PortharcourtElectric"),
                            ("KadunaElectric", "KadunaElectric"),
                            ("KanoElectric", "KanoElectric"),
                            ("YolaElectric", "YolaElectric"),
                            ("Bet9ja", "Bet9ja"),
                            ("BangBet", "BangBet"),
                            ("SupaBet", "SupaBet"),
                            ("CloudBet", "CloudBet"),
                            ("BetLion", "BetLion"),
                            ("1xBet", "1xBet"),
                            ("MerryBet", "MerryBet"),
                            ("BetWay", "BetWay"),
                            ("BetLand", "BetLand"),
                            ("BetKing", "BetKing"),
                            ("LiveScoreBet", "LiveScoreBet"),
                            ("NaijaBet", "NaijaBet"),
                            ("Nin", "Nin"),
                            ("Bvn", "Bvn"),
                            ("PhoneNumberLookup", "PhoneNumberLookup"),
                            ("Waec", "Waec"),
                            ("Jamb", "Jamb"),
                            ("Transfer", "Transfer"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                (
                    "fee_type",
                    models.CharField(
                        choices=[
                            ("percentage", "percentage"),
                            ("fixed", "fixed"),
                            ("banded", "banded"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "cap_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="BusinessFee",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "product",
                    models.CharField(
                        choices=[
                            ("Transfer", "Transfer"),
                            ("Airtime", "Airtime"),
                            ("Data", "Data"),
                            ("Kyc", "Kyc"),
                            ("VirtualAccount", "VirtualAccount"),
                            ("RecurringDebit", "RecurringDebit"),
                            ("CableTv", "CableTv"),
                            ("Epin", "Epin"),
                            ("Education", "Education"),
                            ("Electricity", "Electricity"),
                            ("Betting", "Betting"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("Mtn", "Mtn"),
                            ("Glo", "Glo"),
                            ("Airtel", "Airtel"),
                            ("9Mobile", "9Mobile"),
                            ("Dstv", "Dstv"),
                            ("Gotv", "Gotv"),
                            ("Startimes", "Startimes"),
                            ("AbujaElectric", "AbujaElectric"),
                            ("BeninElectric", "BeninElectric"),
                            ("EnuguElectric", "EnuguElectric"),
                            ("EkoElectric", "EkoElectric"),
                            ("IbadanElectric", "IbadanElectric"),
                            ("IkejaElectric", "IkejaElectric"),
                            ("JosElectric", "JosElectric"),
                            ("PortharcourtElectric", "PortharcourtElectric"),
                            ("KadunaElectric", "KadunaElectric"),
                            ("KanoElectric", "KanoElectric"),
                            ("YolaElectric", "YolaElectric"),
                            ("Bet9ja", "Bet9ja"),
                            ("BangBet", "BangBet"),
                            ("SupaBet", "SupaBet"),
                            ("CloudBet", "CloudBet"),
                            ("BetLion", "BetLion"),
                            ("1xBet", "1xBet"),
                            ("MerryBet", "MerryBet"),
                            ("BetWay", "BetWay"),
                            ("BetLand", "BetLand"),
                            ("BetKing", "BetKing"),
                            ("LiveScoreBet", "LiveScoreBet"),
                            ("NaijaBet", "NaijaBet"),
                            ("Nin", "Nin"),
                            ("Bvn", "Bvn"),
                            ("PhoneNumberLookup", "PhoneNumberLookup"),
                            ("Waec", "Waec"),
                            ("Jamb", "Jamb"),
                            ("Transfer", "Transfer"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                (
                    "fee_type",
                    models.CharField(
                        choices=[
                            ("percentage", "percentage"),
                            ("fixed", "fixed"),
                            ("banded", "banded"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "cap_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=12,
                        null=True,
                    ),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="business_fee",
                        to="business.business",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="BusinessFeeBand",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=12)),
                ("lower_bound", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "upper_bound",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                ("upper_bound_infinite", models.BooleanField(default=False)),
                (
                    "business_fee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="business_fee_bands",
                        to="fees.businessfee",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="VenderFeeBand",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=12)),
                ("lower_bound", models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    "upper_bound",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                ("upper_bound_infinite", models.BooleanField(default=False)),
                (
                    "vender_fee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vender_fee_bands",
                        to="fees.venderfee",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
