# Generated by Django 5.1.7 on 2025-06-13 00:48

from decimal import Decimal

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("fees", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="businessfee",
            name="amount",
            field=models.DecimalField(
                decimal_places=2, default=Decimal("0"), max_digits=12
            ),
        ),
        migrations.AlterField(
            model_name="businessfee",
            name="cap_amount",
            field=models.DecimalField(
                decimal_places=2, default=Decimal("0"), max_digits=12
            ),
        ),
        migrations.AlterField(
            model_name="businessfeeband",
            name="upper_bound",
            field=models.DecimalField(
                decimal_places=2, default=Decimal("0"), max_digits=12
            ),
        ),
        migrations.AlterField(
            model_name="venderfee",
            name="amount",
            field=models.DecimalField(
                decimal_places=2, default=Decimal("0"), max_digits=12
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="venderfee",
            name="cap_amount",
            field=models.DecimalField(
                decimal_places=2, default=Decimal("0"), max_digits=12
            ),
        ),
        migrations.AlterField(
            model_name="venderfeeband",
            name="upper_bound",
            field=models.DecimalField(
                decimal_places=2, default=Decimal("0"), max_digits=12
            ),
        ),
    ]
