# Generated by Django 5.1.7 on 2025-06-16 04:51

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0013_alter_director_nin_document"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BusinessVASProduct",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "product_type",
                    models.CharField(
                        choices=[
                            ("VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"),
                            ("TRANSFER", "TRANSFER"),
                            ("AIRTIME", "AIRTIME"),
                            ("DATA", "DATA"),
                            ("BETTING", "BETTING"),
                            ("ELECTRICITY", "ELECTRICITY"),
                            ("CABLE_TV", "CABLE_TV"),
                            ("SME_DATA", "SME_DATA"),
                            ("KYC", "KYC"),
                            ("EDUCATION", "EDUCATION"),
                            ("EPIN", "EPIN"),
                            ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                        ],
                        max_length=50,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("notes", models.TextField(blank=True, null=True)),
                ("activated_at", models.DateTimeField(auto_now_add=True)),
                ("deactivated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "activated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="activated_vas_products",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="business.business",
                    ),
                ),
                (
                    "deactivated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="deactivated_vas_products",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Business VAS Products",
                "unique_together": {("business", "product_type")},
            },
        ),
    ]
