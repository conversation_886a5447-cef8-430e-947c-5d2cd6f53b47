from business.enums import ChangeRequestStatus, DocumentStatus
from business.handlers.onboarding_workflow import OnboardingWorkflowHandler
from business.models import (
    APIConfig,
    Business,
    BusinessChangeRequest,
    Director,
    Document,
    SettlementDetail,
    SocialMedia,
)
from business.v1.filters import BusinessChangeRequestFilter
from business.v1.serializers import (
    AddDirectorSerializer,
    AddSettlementDetailsChangeRequestSerializer,
    AddSettlementDetailsSerializer,
    AddUpdateDirectorChangeRequestSerializer,
    APIConfigSettingsSerializer,
    BusinessChangeRequestDetailSerializer,
    BusinessChangeRequestMinimalSerializer,
    BusinessInformationChangeRequestSerializer,
    BusinessInformationSerializer,
    DirectorListSerializer,
    DocumentMiniSerializer,
    GeneratePrivateKeySerializer,
    MultipleSocialMediaSerializer,
    SettlementDetailsSerializer,
    SocialMediaSerializer,
    UploadDocumentChangeRequestSerializer,
    UploadDocumentSerializer,
    VerifyAccountNumberSerializer,
)
from common.decorators import merchant_onboarding_required
from common.serializers import EmptySerializer
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from user.models import User


class OnboardBusinessViewSet(viewsets.GenericViewSet):
    """
    ┌─────────────────────────────┐
    │  Business Onboarding Stages │
    └─────────────────────────────┘

    1️⃣  Business Information
        - Collect basic business details (name, email, phone, etc.)

    2️⃣  Documentation
        - Upload and verify required documents

    3️⃣  Directors Details
        - Capture personal and identification information of key individuals

    4️⃣  Settlement Details
        - Provide bank or payment account information for settlements

    📌 Each stage must be completed in order to successfully onboard a business.
    """

    permission_classes = [IsAuthenticated]
    queryset = User.objects.all()

    @action(
        methods=["POST"],
        detail=False,
        url_path="submit-information",
        serializer_class=BusinessInformationSerializer,
    )
    def submit_business_information(self, request):
        business: Business = request.user.business
        serializer = BusinessInformationSerializer(business, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Business information submitted successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-information",
        serializer_class=BusinessInformationSerializer,
    )
    def business_information(self, request):
        business: Business = request.user.business
        serializer = BusinessInformationSerializer(business)
        return Response(
            {"success": True, "data": serializer.data},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="submit-information-change-request",
        serializer_class=BusinessInformationChangeRequestSerializer,
    )
    @merchant_onboarding_required
    def submit_information_request(self, request):
        business: Business = request.user.business
        serializer = self.get_serializer(instance=business, data=request.data)
        serializer.is_valid(raise_exception=True)
        change_request = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_request).data

        return Response(
            {
                "success": True,
                "message": "Business information change request submitted.",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="upload-documents",
        serializer_class=UploadDocumentSerializer,
    )
    def upload_documents(self, request):
        user: User = request.user
        serializer = UploadDocumentSerializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Document uploaded successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="upload-documents-change-request",
        serializer_class=UploadDocumentChangeRequestSerializer,
    )
    def submit_document_change_request(self, request):
        user: User = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        change_request = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_request).data

        return Response(
            {
                "success": True,
                "message": "Document change request submitted successfully",
                "data": data,
            },
            status=status.HTTP_201_CREATED,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-documents",
        serializer_class=DocumentMiniSerializer,
    )
    def fetch_documents(self, request):

        business: Business = request.user.business
        documents = (
            Document.objects.filter(business=business)
            .exclude(status=DocumentStatus.ChangeRequested.value)
            .order_by("document_name")
        )

        return Response(
            {
                "success": True,
                "data": DocumentMiniSerializer(documents, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-director",
        serializer_class=AddDirectorSerializer,
    )
    def add_director(self, request):
        user: User = request.user
        serializer = AddDirectorSerializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Director details submitted successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-or-update-director-change-request",
        serializer_class=AddUpdateDirectorChangeRequestSerializer,
    )
    @merchant_onboarding_required
    def submit_director_change_request(self, request):
        business: Business = request.user.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        change_request = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_request).data

        return Response(
            {
                "success": True,
                "message": "Business Settlement Details change request submitted.",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-directors",
        serializer_class=DirectorListSerializer,
    )
    def fetch_directors(self, request):
        """Fetch all directors for the current business"""
        business: Business = request.user.business
        directors = Director.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": DirectorListSerializer(directors, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["DELETE"],
        detail=False,
        url_path="delete-director/(?P<director_id>[^/.]+)",
        serializer_class=EmptySerializer,
    )
    def delete_director(self, request, director_id=None):
        business: Business = request.user.business

        try:
            director = Director.objects.get(id=director_id, business=business)
            director.delete()

            return Response(
                {"success": True, "message": "Director deleted successfully"},
                status=status.HTTP_200_OK,
            )
        except Director.DoesNotExist:
            return Response(
                {"success": False, "message": "Director not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-settlement-details",
        serializer_class=AddSettlementDetailsSerializer,
    )
    def add_settlement_details(self, request):
        user: User = request.user
        serializer = AddSettlementDetailsSerializer(
            data=request.data, context={"user": user}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Settlement details submitted successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-settlement-details-change-request",
        serializer_class=AddSettlementDetailsChangeRequestSerializer,
    )
    @merchant_onboarding_required
    def submit_settlement_details_change_request(self, request):
        business: Business = request.user.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        change_request = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_request).data

        return Response(
            {
                "success": True,
                "message": "Business Settlement Details change request submitted.",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-settlement-details",
        serializer_class=SettlementDetailsSerializer,
    )
    def fetch_settlement_details(self, request):
        business: User = request.user.business

        settlement_details = SettlementDetail.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": SettlementDetailsSerializer(settlement_details, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="verify-account-number",
        serializer_class=VerifyAccountNumberSerializer,
    )
    def verify_account_number(self, request):
        serializer = VerifyAccountNumberSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        result = serializer.save()

        return Response(
            {"success": True, "data": result},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="bank-list",
        serializer_class=EmptySerializer,
    )
    def get_bank_list(self, request):
        # TODO fetch from the bank model
        return Response(
            {
                "success": True,
                "data": [{"bank_name": "Sagecloud Test Bank", "bank_code": "012345"}],
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="onboarding-stages",
        serializer_class=EmptySerializer,
    )
    def onboarding_stages(self, request):
        business: Business = request.user.business
        return Response(
            OnboardingWorkflowHandler(business).onboarding_stages(),
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-social-media",
        serializer_class=MultipleSocialMediaSerializer,
    )
    def add_social_media(self, request):
        """Add or update a social media channel for the business"""
        user: User = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Social media channel added successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="social-media",
        serializer_class=SocialMediaSerializer,
    )
    def list_social_media(self, request):
        """List all social media channels for the business"""
        business: Business = request.user.business
        social_media = SocialMedia.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": SocialMediaSerializer(social_media, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["DELETE"],
        detail=False,
        url_path="social-media/(?P<social_media_id>[^/.]+)",
        serializer_class=EmptySerializer,
    )
    # TODO: Maybe we take this out??
    def delete_social_media(self, request, social_media_id=None):
        """Delete a social media channel"""
        business: Business = request.user.business

        try:
            social_media = SocialMedia.objects.get(
                id=social_media_id, business=business
            )
            social_media.delete()

            return Response(
                {
                    "success": True,
                    "message": "Social media channel deleted successfully",
                },
                status=status.HTTP_200_OK,
            )
        except SocialMedia.DoesNotExist:
            return Response(
                {
                    "success": False,
                    "message": "Social media channel not found",
                },
                status=status.HTTP_404_NOT_FOUND,
            )


class APIConfigViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    queryset = APIConfig.objects.all()

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-settings",
        serializer_class=EmptySerializer,
    )
    def get_config_settings(self, request):
        business: Business = request.user.business
        api_config = (
            APIConfig.objects.values(
                "public_key", "webhook_url", "webhook_signature", "whitelisted_ips"
            )
            .filter(business=business)
            .first()
        )

        return Response({"success": True, "data": api_config})

    @action(
        methods=["POST"],
        detail=False,
        url_path="generate-private-key",
        serializer_class=GeneratePrivateKeySerializer,
    )
    @merchant_onboarding_required
    def generate_private_key(self, request):
        user: User = request.user
        serializer = GeneratePrivateKeySerializer(
            data=request.data, context={"user": user}
        )
        serializer.is_valid(raise_exception=True)
        private_key = serializer.save()

        # Log API key generation
        from audit.utils import log_api_key_generation
        log_api_key_generation(
            request=request,
            user=user,
            business=user.business,
            key_type="private"
        )

        return Response(
            {
                "success": True,
                "data": {"private_key": private_key},
            }
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="settings",
        serializer_class=APIConfigSettingsSerializer,
    )
    @merchant_onboarding_required
    def config_settings(self, request):
        business: Business = request.user.business
        serializer = APIConfigSettingsSerializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {
                "success": True,
                "message": "Settings updated successfully.",
            }
        )


class BusinessChangeRequestViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    queryset = BusinessChangeRequest.objects.all()
    serializer_class = BusinessChangeRequestMinimalSerializer
    filter_backends = [
        DjangoFilterBackend,
    ]
    filterset_class = BusinessChangeRequestFilter
    pagination_class = None

    def get_serializer_class(self):
        if self.action == "retrieve":
            return BusinessChangeRequestDetailSerializer
        return super().get_serializer_class()

    @merchant_onboarding_required
    def list(self, request):
        business: Business = request.user.business
        queryset = self.filter_queryset(self.get_queryset())
        queryset = queryset.filter(business=business).order_by("-created_at")
        data = self.serializer_class(queryset, many=True).data

        return Response(
            {
                "success": True,
                "data": data,
            }
        )

    @merchant_onboarding_required
    def retrieve(self, request, pk=None):
        business: Business = request.user.business
        try:
            change_request = self.get_queryset().get(pk=pk, business=business)
        except BusinessChangeRequest.DoesNotExist:
            return Response(
                {"success": False, "message": "Change request not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

        data = (self.get_serializer(change_request).data,)
        return Response(
            {
                "success": True,
                "data": data,
            }
        )

    @merchant_onboarding_required
    def destroy(self, request, pk=None):
        business = request.user.business
        change_request = get_object_or_404(BusinessChangeRequest, pk=pk)

        if change_request.business != business:
            return Response(
                {
                    "success": False,
                    "message": "You do not have permission to delete this request.",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if change_request.status != ChangeRequestStatus.Pending.value:
            return Response(
                {"success": False, "message": "Only pending requests can be deleted."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        change_request.delete()

        return Response(
            {"success": True, "message": "Change request deleted successfully."},
            status=status.HTTP_200_OK,
        )
