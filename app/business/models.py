import base64
import logging
import secrets

from business.enums import (
    BusinessSection,
    BusinessStatus,
    ChangeRequestStatus,
    ChangeRequestType,
    DocumentName,
    DocumentStatus,
    OnboardingStage,
    SocialMediaChannel,
)
from common.models import AuditableModel
from django.db import IntegrityError, models
from django.utils import timezone
from transaction.enums import TransactionClassEnum
from user.models import User
from wallet.models import Wallet, WalletEnums

logger = logging.getLogger(__name__)


class Business(AuditableModel):
    """Business model for storing business information"""

    name = models.CharField(max_length=255, null=True, blank=True)
    owner = models.OneToOneField(
        "user.User", on_delete=models.PROTECT, related_name="business"
    )
    email = models.EmailField(unique=True, db_index=True, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    phone = models.CharField(max_length=20, unique=True, null=True, blank=True)
    website = models.URLField(unique=True, null=True, blank=True)

    status = models.CharField(
        choices=BusinessStatus.choices(),
        null=True,
        blank=True,
        max_length=20,
        default=BusinessStatus.Inactive.value,
    )

    # Address Info
    office_address = models.CharField(max_length=255, null=True, blank=True)
    street = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=50, null=True, blank=True)
    state = models.CharField(max_length=50, null=True, blank=True)
    postal_code = models.CharField(max_length=20, null=True, blank=True)

    rc_number = models.CharField(max_length=20, unique=True, null=True, blank=True)

    onboarding_stage = models.CharField(
        max_length=40,
        null=True,
        blank=True,
        choices=OnboardingStage.choices,
        default=OnboardingStage.BusinessInformation.value,
    )

    objects = models.Manager()

    def __str__(self):
        if self.name is None:
            return f"UNNAMED BUSINESS -- {self.owner.email}"
        return f"{self.name.upper()} -- {self.owner.email}"

    @property
    def full_address(self):
        return f"{self.office_address}, {self.street}, {self.city}, {self.state}, {self.postal_code}"

    def activate(self):
        if self.status != BusinessStatus.Active.value:
            self.status = BusinessStatus.Active.value
            self.save(update_fields=["status"])
            return True
        return False

    def deactivate(self):
        if self.status != BusinessStatus.Inactive.value:
            self.status = BusinessStatus.Inactive.value
            self.save(update_fields=["status"])
            return True
        return False

    def verify(self):
        if self.status != BusinessStatus.Verified.value:
            self.status = BusinessStatus.Verified.value
            self.save(update_fields=["status"])
            return True
        return False

    def _create_core_wallets(self):
        for wallet_type in [WalletEnums.GENERAL, WalletEnums.COMMISSION]:
            try:
                Wallet.objects.get_or_create(
                    business=self,
                    type=wallet_type,
                    defaults={"balance": 0},
                )
            except IntegrityError:
                continue

    def _get_wallet(self, wallet_type, for_update=False):
        qs = self.wallets
        if for_update:
            qs = qs.select_for_update()
        return qs.get(type=wallet_type)

    def get_general_wallet(self, for_update=False):
        return self._get_wallet(WalletEnums.GENERAL, for_update=for_update)

    def get_commission_wallet(self, for_update=False):
        return self._get_wallet(WalletEnums.COMMISSION, for_update=for_update)

    def get_kolomoni_va_wallet(self, for_update=False):
        return self._get_wallet(
            WalletEnums.KOLOMONI_VIRTUAL_ACCOUNT, for_update=for_update
        )

    def get_wema_va_wallet(self, for_update=False):
        return self._get_wallet(WalletEnums.WEMA_VIRTUAL_ACCOUNT, for_update=for_update)

    def get_access_va_wallet(self, for_update=False):
        return self._get_wallet(
            WalletEnums.ACCESS_VIRTUAL_ACCOUNT, for_update=for_update
        )

    class Meta:
        verbose_name_plural = "Businesses"
        ordering = ["-created_at"]


class Document(AuditableModel):
    document = models.FileField(
        upload_to="documents",
    )
    document_name = models.CharField(max_length=250, db_index=True)
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="documents",
    )
    status = models.CharField(
        choices=DocumentStatus.choices(),
        max_length=100,
        default=DocumentStatus.Pending.value,
    )
    rejection_message = models.CharField(max_length=500, null=True, blank=True)
    approved_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name="+"
    )
    rejected_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name="+"
    )
    approved_at = models.DateTimeField(null=True, blank=True)
    rejected_at = models.DateTimeField(null=True, blank=True)
    resubmitted_at = models.DateTimeField(null=True, blank=True)

    parent_document = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="change_requests",
    )

    objects = models.Manager()

    class Meta:
        ordering = ("-created_at",)

    def __str__(self):
        if self.business.name is None:
            return f"UNNAMED BUSINESS -- {self.document_name} -- {self.document.name}"
        return f"{self.business.name} -- {self.document_name} -- {self.document.name}"

    def delete(self):
        try:
            self.document.delete(save=False)  # save=False prevents a database save
            logger.info(
                f"Successfully deleted file for old document ID: {self.id} - {self.document_name}"
            )
        except Exception as e:
            logger.error(
                f"Error deleting file for old document ID: {self.id} - {str(e)}"
            )

        try:
            self.delete()
            logger.info(
                f"Successfully deleted database record for document ID: {self.id}"
            )
        except Exception as e:
            logger.error(
                f"Error deleting database record for document ID: {self.id} - {str(e)}"
            )

    def delete_parent(self):
        try:
            self.parent_document.delete()
            logger.info(
                f"Successfully deleted database record for document ID: {self.id}"
            )
        except Exception as e:
            logger.error(
                f"Error deleting database record for document ID: {self.id} - {str(e)}"
            )

    def approve(self, admin_user):
        self.status = DocumentStatus.Approved.value
        self.approved_by = admin_user
        self.approved_at = timezone.now()
        self.save(update_fields=["status", "approved_by", "approved_at"])

    def reject(self, admin_user, message):
        self.status = DocumentStatus.Rejected.value
        self.rejected_by = admin_user
        self.rejected_at = timezone.now()
        self.rejection_message = message
        self.save(
            update_fields=["status", "rejected_by", "rejected_at", "rejection_message"]
        )


class Director(AuditableModel):
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="directors",
    )
    name = models.CharField(max_length=250, db_index=True)
    email = models.EmailField(db_index=True)
    phone = models.CharField(max_length=20)
    bvn = models.CharField(max_length=20)
    nin_document = models.ForeignKey(
        "Document", null=True, blank=True, on_delete=models.SET_NULL
    )
    objects = models.Manager()

    class Meta:
        ordering = ("-created_at",)

    def __str__(self):
        if self.business.name is None:
            return f"UNNAMED BUSINESS -- {self.name}"
        return f"{self.business.name} -- {self.name}"


class SettlementDetail(AuditableModel):
    bank_name = models.CharField(max_length=50)
    bank_code = models.CharField(max_length=10)
    account_number = models.CharField(max_length=10, db_index=True)
    account_name = models.CharField(max_length=100, null=True)
    is_active = models.BooleanField(default=True)
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="settlement_details",
        null=True,
    )

    objects = models.Manager()

    class Meta:
        ordering = ("-created_at",)

    def __str__(self):
        return f"{self.business.name} -- {self.account_number} --- {self.account_name} --- {self.bank_name}"


class SocialMedia(AuditableModel):
    """Model for storing business social media channels"""

    business = models.ForeignKey(
        "business.Business",
        on_delete=models.CASCADE,
        related_name="social_media_channels",
    )
    channel = models.CharField(
        max_length=50,
        choices=SocialMediaChannel.choices(),
    )
    url = models.URLField(max_length=255)

    objects = models.Manager()

    class Meta:
        ordering = ("-created_at",)
        verbose_name_plural = "Social Media Channels"
        unique_together = ("business", "channel")

    def __str__(self):
        return f"{self.business.name} - {self.channel}"


class APIConfig(AuditableModel):
    business = models.ForeignKey(
        "business.Business", on_delete=models.CASCADE, related_name="business_api_keys"
    )
    private_key = models.CharField(null=True, blank=True)
    public_key = models.CharField(null=True, blank=True, db_index=True)
    webhook_url = models.URLField(null=True, blank=True)
    webhook_signature = models.TextField(null=True, blank=True)
    whitelisted_ips = models.CharField(null=True, blank=True)

    @staticmethod
    def generate_keys():

        public_key = secrets.token_bytes(32)
        private_key = secrets.token_bytes(64)

        return (
            base64.urlsafe_b64encode(public_key).decode("utf-8"),
            base64.urlsafe_b64encode(private_key).decode("utf-8"),
        )

    def __str__(self):
        return self.business.name


class BusinessChangeRequest(AuditableModel):
    SECTION_CHOICES = BusinessSection.choices()

    business = models.ForeignKey(
        "business.Business", on_delete=models.CASCADE, related_name="change_requests"
    )
    section = models.CharField(max_length=100, choices=SECTION_CHOICES)
    object_id = models.CharField(null=True, blank=True)
    old_value = models.JSONField()
    new_value = models.JSONField()
    change_type = models.CharField(
        max_length=20,
        choices=ChangeRequestType.choices(),
        default=ChangeRequestType.Modification.value,
    )
    rejection_note = models.TextField(null=True, blank=True)
    status = models.CharField(
        max_length=30,
        choices=ChangeRequestStatus.choices(),
        default=ChangeRequestStatus.Pending,
    )
    created_by = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True)
    reviewed_by = models.ForeignKey(
        "user.User", null=True, blank=True, on_delete=models.SET_NULL, related_name="+"
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name_plural = "Business Change Requests"
        constraints = [
            models.UniqueConstraint(
                fields=["business", "section"],
                condition=models.Q(status=ChangeRequestStatus.Pending),
                name="unique_pending_request_per_section",
            )
        ]

    def __str__(self):
        if self.business.name is None:
            return f"UNNAMED BUSINESS -- {self.section} -- {self.status} -- {self.change_type}"
        return f"{self.business.name.upper()} - {self.section} -- {self.status} -- {self.change_type}"

    def approve(self, reviewed_by):
        """
        Apply the requested changes to the relevant Business section and mark as approved.
        """
        if self.status != ChangeRequestStatus.Pending.value:
            raise ValueError("Only pending requests can be approved.")

        match self.section:
            case BusinessSection.BusinessInformation.value:
                self._apply_approved_business_information_changes()
            case BusinessSection.SettlementDetails.value:
                self._apply_approved_settlement_details_changes()
            case BusinessSection.DirectorsAndOwners.value:
                self._apply_approved_director_changes()
            case (
                BusinessSection.DocumentationProofOfAddress.value
                | BusinessSection.DocumentationCertificateOfIncorporation.value
                | BusinessSection.DocumentationMemorandumOfAssociation.value
            ):
                self._apply_approved_documentation_changes(reviewed_by)
            case _:
                raise ValueError(f"Unsupported change request section: {self.section}")

        self.status = ChangeRequestStatus.Approved.value
        self.reviewed_by = reviewed_by
        self.reviewed_at = timezone.now()
        self.save()

    def reject(self, reviewed_by, note=None):
        """
        Mark the change request as rejected, optionally with a note.
        """
        if self.status != ChangeRequestStatus.Pending.value:
            raise ValueError("Only pending requests can be rejected.")

        match self.section:
            case BusinessSection.BusinessInformation.value:
                self._apply_rejected_business_information_changes(reviewed_by, note)
            case BusinessSection.SettlementDetails.value:
                self._apply_rejected_settlement_details_changes(reviewed_by, note)
            case BusinessSection.DirectorsAndOwners.value:
                self._apply_rejected_director_changes(reviewed_by, note)
            case (
                BusinessSection.DocumentationProofOfAddress.value
                | BusinessSection.DocumentationCertificateOfIncorporation.value
                | BusinessSection.DocumentationMemorandumOfAssociation.value
            ):
                self._apply_rejected_documentation_changes(reviewed_by, note)
            case _:
                raise ValueError(f"Unsupported change request section: {self.section}")

        self.status = ChangeRequestStatus.Rejected.value
        self.reviewed_by = reviewed_by
        self.reviewed_at = timezone.now()
        if note:
            self.rejection_note = note
        self.save()

    def _apply_approved_business_information_changes(self):
        """
        Applies changes to the Business model based on new_value JSON.
        """
        for field, value in (self.new_value or {}).items():
            setattr(self.business, field, value)
        self.business.save()

    def _apply_rejected_business_information_changes(self, reviewed_by, message=None):
        self.status = ChangeRequestStatus.Rejected.value
        self.reviewed_by = reviewed_by
        self.reviewed_at = timezone.now()
        if message:
            self.rejection_note = message
        self.save()

    def _apply_approved_settlement_details_changes(self):
        """
        Applies changes to the existing active SettlementDetail for the business.
        If one exists, it's updated; otherwise, a new one is created.
        """
        current_settlement = SettlementDetail.objects.filter(
            business=self.business, is_active=True
        ).first()

        if current_settlement:
            for field, value in (self.new_value or {}).items():
                setattr(current_settlement, field, value)
            current_settlement.save()
        else:
            SettlementDetail.objects.create(
                business=self.business, is_active=True, **(self.new_value or {})
            )

    def _apply_rejected_settlement_details_changes(self, reviewed_by, message=None):
        self.status = ChangeRequestStatus.Rejected.value
        self.reviewed_by = reviewed_by
        self.reviewed_at = timezone.now()
        if message:
            self.rejection_note = message
        self.save()

    def _apply_approved_director_changes(self):
        """
        Applies changes to a Director model instance.
        - If object_id is provided: updates the existing Director.
        - If object_id is None: creates a new Director.
        - Handles special handling for nin_file (Document FK).
        """
        data = self.new_value or {}
        nin_file_id = data.get("nin_file")
        if nin_file_id:
            try:
                data["nin_file"] = Document.objects.get(pk=nin_file_id)
            except Document.DoesNotExist:
                raise ValueError(f"Document with ID {nin_file_id} does not exist.")

        if self.object_id:
            try:
                director = Director.objects.get(
                    pk=self.object_id, business=self.business
                )
            except Director.DoesNotExist:
                raise ValueError(f"Director with ID {self.object_id} does not exist.")
            for field, value in data.items():
                setattr(director, field, value)
            director.save()
        else:
            Director.objects.create(business=self.business, **data)

    def _apply_rejected_director_changes(self, reviewed_by, message=None):
        """
        Applies changes to the Business model based on new_value JSON.
        """
        if not self.object_id:
            existing_doc = self.business.documents.filter(
                status=DocumentStatus.ChangeRequested.value,
                document_name=DocumentName.DirectorNin.value,
            ).first()
            if existing_doc:
                existing_doc.delete()
                logger.info(
                    f"Deleted existing NIN document for BusinessChangeRequest ID {self.pk}."
                )
            else:
                logger.warning(
                    f"No existing NIN document found for BusinessChangeRequest ID {self.pk}."
                )
            return
        try:
            director = Director.objects.get(pk=self.object_id, business=self.business)
        except Director.DoesNotExist:
            logger.error(
                f"Director with ID {self.object_id} does not exist for BusinessChangeRequest ID {self.pk}."
            )
            raise ValueError(f"Director with ID {self.object_id} does not exist.")

        if director.nin_file:
            director.nin_file.delete()
            logger.info(
                f"Deleted NIN document for director ID {director.id} for BusinessChangeRequest ID {self.pk}."
            )
        else:
            logger.warning(
                f"No NIN document found for director ID {director.id} for BusinessChangeRequest ID {self.pk}."
            )

    def _apply_approved_documentation_changes(self, reviewed_by):
        """
        Applies changes related to a Document instance when the BusinessChangeRequest is approved.
        """
        if not self.object_id:
            logger.warning(
                f"BusinessChangeRequest ID {self.pk} has no object_id. "
                "Skipping document application. This might indicate an issue or a different type of request."
            )
            raise ValueError(
                "BusinessChangeRequest for documentation changes must have an object_id."
            )

        try:
            new_document: Document = Document.objects.get(
                pk=self.object_id,
                business=self.business,
                status=DocumentStatus.ChangeRequested.value,
            )
            logger.info(
                f"Applying changes for new document ID: {new_document.id}, Name: {new_document.document_name}"
            )
        except Document.DoesNotExist:
            raise ValueError(
                f"Document with ID {self.object_id} does not exist or is not a pending change request."
            )

        try:
            admin_user = reviewed_by
            logger.info(
                f"Applying changes for BusinessChangeRequest ID {self.pk} via helper function."
            )
            self._approve_document_change_request(new_document, admin_user)
            logger.info(
                f"Successfully applied document changes for BusinessChangeRequest ID {self.pk}."
            )
        except Exception as e:
            logger.error(
                f"Error applying document changes for BusinessChangeRequest ID {self.pk}: {e}"
            )
            raise

    def _apply_rejected_documentation_changes(self, reviewed_by, message):
        if not self.object_id:
            logger.warning(
                f"BusinessChangeRequest ID {self.pk} has no object_id. "
                "Skipping document application. This might indicate an issue or a different type of request."
            )
            raise ValueError(
                "BusinessChangeRequest for documentation changes must have an object_id."
            )

        try:
            new_document: Document = Document.objects.get(
                pk=self.object_id,
                business=self.business,
                status=DocumentStatus.ChangeRequested.value,
            )
            logger.info(
                f"Applying changes for new document ID: {new_document.id}, Name: {new_document.document_name}"
            )
        except Document.DoesNotExist:
            raise ValueError(
                f"Document with ID {self.object_id} does not exist or is not a pending change request."
            )

        try:
            admin_user = reviewed_by
            logger.info(
                f"Applying changes for BusinessChangeRequest ID {self.pk} via helper function."
            )
            self._reject_document_change_request(new_document, admin_user)
            logger.info(
                f"Successfully applied document changes for BusinessChangeRequest ID {self.pk}."
            )
        except Exception as e:
            logger.error(
                f"Error applying document changes for BusinessChangeRequest ID {self.pk}: {e}"
            )
            raise

    @staticmethod
    def _approve_document_change_request(
        new_document_instance: Document, admin_user: User
    ):
        new_document_instance.approve(admin_user)
        new_document_instance.delete_parent()
        logger.info(
            f"Document ID {new_document_instance.id} approved for {new_document_instance.document_name}"
        )

    @staticmethod
    def _reject_document_change_request(
        document_instance: Document, admin_user: User, message: str
    ):
        document_instance.reject(admin_user, message)
        document_instance.delete()
        logger.info(
            f"Document ID {document_instance.id} rejected for {document_instance.document_name}"
        )


class BusinessVASProduct(AuditableModel):
    business = models.ForeignKey("business.Business", on_delete=models.CASCADE)
    product_type = models.CharField(
        max_length=50, choices=TransactionClassEnum.choices()
    )
    is_active = models.BooleanField(default=True)

    activated_by = models.ForeignKey(
        "user.User",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="activated_vas_products",
    )
    deactivated_by = models.ForeignKey(
        "user.User",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="deactivated_vas_products",
    )

    notes = models.TextField(null=True, blank=True)
    activated_at = models.DateTimeField(auto_now_add=True)
    deactivated_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ("business", "product_type")
        verbose_name_plural = "Business VAS Products"

    def __str__(self):
        if self.business.name is None:
            return f"UNNAMED BUSINESS -- {self.product_type}"
        return f"{self.business.name} - {self.product_type}"
