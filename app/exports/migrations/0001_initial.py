# Generated by Django 5.1.7 on 2025-06-13 14:43

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("business", "0007_business_status_apiconfig"),
        ("contenttypes", "0002_remove_content_type_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ExportField",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("model_name", models.CharField(db_index=True, max_length=100)),
                ("field_name", models.CharField(max_length=100)),
                ("field_label", models.CharField(max_length=200)),
                ("field_type", models.CharField(max_length=50)),
                ("is_default", models.BooleanField(default=False)),
                ("is_sensitive", models.BooleanField(default=False)),
                ("order", models.IntegerField(default=0)),
            ],
            options={
                "ordering": ["model_name", "order", "field_name"],
                "unique_together": {("model_name", "field_name")},
            },
        ),
        migrations.CreateModel(
            name="ExportRequest",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "export_type",
                    models.CharField(
                        choices=[
                            ("transactions", "Transactions"),
                            ("audit_logs", "Audit Logs"),
                            ("commissions", "Commissions"),
                            ("custom", "Custom"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                ("model_name", models.CharField(db_index=True, max_length=100)),
                (
                    "fields_to_export",
                    models.JSONField(
                        help_text="List of field names to include in export"
                    ),
                ),
                (
                    "filters",
                    models.JSONField(
                        default=dict, help_text="Filters applied to the queryset"
                    ),
                ),
                ("filename", models.CharField(max_length=255)),
                ("file_path", models.CharField(blank=True, max_length=500, null=True)),
                ("file_size", models.BigIntegerField(blank=True, null=True)),
                ("download_url", models.URLField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("expired", "Expired"),
                        ],
                        db_index=True,
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("total_records", models.IntegerField(blank=True, null=True)),
                ("processed_records", models.IntegerField(default=0)),
                ("error_message", models.TextField(blank=True, null=True)),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the download link expires",
                        null=True,
                    ),
                ),
                ("email_sent", models.BooleanField(default=False)),
                ("email_sent_at", models.DateTimeField(blank=True, null=True)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="export_requests",
                        to="business.business",
                    ),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="export_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created_at"],
                        name="exports_exp_user_id_f20cc9_idx",
                    ),
                    models.Index(
                        fields=["business", "-created_at"],
                        name="exports_exp_busines_1ee460_idx",
                    ),
                    models.Index(
                        fields=["status", "-created_at"],
                        name="exports_exp_status_84a492_idx",
                    ),
                    models.Index(
                        fields=["export_type", "-created_at"],
                        name="exports_exp_export__929373_idx",
                    ),
                ],
            },
        ),
    ]
