import logging

from business.models import Business
from common.dtos import CoreServiceResponse
from common.enums import AirtimeNetworkEnum, CoreServiceResponseStatus, VasGateStatus
from django.db import transaction
from rest_framework import serializers
from transaction.dtos import CreateBaseTransactionParams
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.handlers.base import TransactionHandler
from transaction.models.airtime import AirtimeVASTransaction
from transaction.tasks import process_commission
from vas.integrations.airtime import AirtimeVASGateClient

logger = logging.getLogger(__name__)


class AirtimePurchaseRequestSerializer(serializers.Serializer):
    network = serializers.ChoiceField(choices=AirtimeNetworkEnum.choices())
    reference = serializers.CharField()
    phone = serializers.CharField()
    amount = serializers.IntegerField()

    def save(self, **kwargs):
        payload = self.validated_data
        network = payload.get("network")
        reference = payload.get("reference")
        phone_number = payload.get("phone")

        business: Business = self.context["business"]
        wallet = business.get_general_wallet()

        handler = TransactionHandler()

        params = self.__create_base_txn_params(wallet, business, reference, payload)
        txn = handler.debit_wallet(params)

        vas_extra_fields = {
            "network": network,
            "phone_number": phone_number,
        }
        vas_txn, ledger_txn = handler.create_vas_transaction_and_ledger_entry(
            txn=txn,
            txn_class=TransactionClassEnum.AIRTIME.value,
            extra_fields=vas_extra_fields,
        )

        client = AirtimeVASGateClient()
        payload["reference"] = txn.reference
        _, response = client.purchase_airtime(payload)
        response_status = response.get("status")

        if response_status == VasGateStatus.Success.value:

            with transaction.atomic():
                txn.status = TransactionStatusEnum.SUCCESSFUL.value
                txn.save()

                handler.update_vas_transaction(txn, vas_txn, {"status": txn.status})

                process_commission.delay_on_commit(txn.id, business.id)

            return CoreServiceResponse(
                success=True,
                status=CoreServiceResponseStatus.Success.value,
                message="Airtime purchased successfully.",
                reference=reference,
                data=response.get("data", None),
            )

        if response_status == VasGateStatus.Failed.value:

            with transaction.atomic():
                txn.status = TransactionStatusEnum.FAILED.value
                txn.save()

                handler.update_vas_transaction(txn, vas_txn, {"status": txn.status})

                # @TODO: process reversal

            return CoreServiceResponse(
                success=False,
                status=CoreServiceResponseStatus.Failed.value,
                message=response.get("message", None) or "Airtime purchased failed.",
                reference=reference,
                data=response.get("data", None),
            )

        return CoreServiceResponse(
            success=False,
            status=CoreServiceResponseStatus.Pending.value,
            message="Airtime purchase pending.",
            reference=reference,
            data=response.get("data", None),
        )

    @staticmethod
    def __create_base_txn_params(wallet, business, reference, payload):
        amount = payload.get("amount")
        network = payload.get("network")
        phone = payload.get("phone")
        return CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.AIRTIME.value,
            type=network,
            narration=f"N{amount} {network} Airtime Purchase on {phone}",
            reference=reference,
        )


class AirtimeVASTransactionSerializer(serializers.ModelSerializer):

    class Meta:
        model = AirtimeVASTransaction
        fields = "__all__"
