# Generated by Django 5.1.7 on 2025-06-20 00:47

import common.kgs
import django.db.models.deletion
import django.utils.timezone
import transaction.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0018_document_parent_document_alter_document_status"),
        ("transaction", "0023_alter_commissiontransaction_options"),
        ("wallet", "0003_alter_wallet_type"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="transaction",
            name="merchant_reference",
            field=models.CharField(db_index=True, max_length=100),
        ),
        migrations.CreateModel(
            name="FundsTransferVasTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                ("recipient_account_number", models.CharField(max_length=10)),
                ("recipient_account_name", models.CharField(max_length=255)),
                ("recipient_bank_name", models.CharField(max_length=255)),
                ("recipient_bank_code", models.CharField(max_length=6)),
                (
                    "session_id",
                    models.CharField(
                        blank=True, db_index=True, max_length=40, null=True
                    ),
                ),
                (
                    "transaction_id",
                    models.CharField(
                        blank=True, db_index=True, max_length=40, null=True
                    ),
                ),
                (
                    "requery_response_code",
                    models.CharField(
                        help_text="Response for Requery", max_length=5, null=True
                    ),
                ),
                (
                    "requery_response_message",
                    models.CharField(
                        help_text="requery_response_message", max_length=5, null=True
                    ),
                ),
                ("requery_response", models.JSONField(null=True)),
                ("requery_retries_count", models.IntegerField(default=0)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Funds Transfer Transaction",
                "verbose_name_plural": "Funds Transfer Transactions",
                "ordering": ("-created_at",),
            },
        ),
    ]
