# Generated by Django 5.1.7 on 2025-06-18 08:21

import common.enums
import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0013_alter_director_nin_document"),
        ("transaction", "0020_remove_cabletvvastransaction_code_and_more"),
        ("wallet", "0003_alter_wallet_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="transaction",
            name="vender",
            field=models.CharField(
                blank=True,
                choices=common.enums.VenderEnum.choices,
                db_index=True,
                max_length=50,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="CommissionTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "source_transaction_reference",
                    models.CharField(db_index=True, max_length=100),
                ),
                ("reference", models.CharField(db_index=True, max_length=100)),
                (
                    "txn_class",
                    models.CharField(
                        choices=[
                            ("VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"),
                            ("TRANSFER", "TRANSFER"),
                            ("AIRTIME", "AIRTIME"),
                            ("DATA", "DATA"),
                            ("BETTING", "BETTING"),
                            ("ELECTRICITY", "ELECTRICITY"),
                            ("CABLE_TV", "CABLE_TV"),
                            ("SME_DATA", "SME_DATA"),
                            ("KYC", "KYC"),
                            ("EDUCATION", "EDUCATION"),
                            ("EPIN", "EPIN"),
                            ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                        ],
                        max_length=30,
                    ),
                ),
                ("narration", models.TextField()),
                ("old_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                ("new_balance", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="commission_histories",
                        to="business.business",
                    ),
                ),
                (
                    "source_transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="commission_histories",
                        to="transaction.transaction",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="commission_histories",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
