from console.v1.views import (
    AdminBusinessChangeRequestViewSet,
    AdminBusinessManagementViewSet,
)
from django.urls import include, path
from rest_framework.routers import DefaultRouter
from transaction.v1.views import AdminTransactionViewSet

app_name = "console"

router = DefaultRouter()
router.register(
    "business", AdminBusinessManagementViewSet, basename="business-management"
)
router.register("transactions", AdminTransactionViewSet, basename="admin-transactions")
router.register(
    "change-requests",
    AdminBusinessChangeRequestViewSet,
    basename="business-change-requests",
)

urlpatterns = [
    path("", include(router.urls)),
    path(
        "business/<str:business_id>/transactions/",
        AdminTransactionViewSet.as_view({"get": "list"}),
        name="business-transactions-list",
    ),
]
