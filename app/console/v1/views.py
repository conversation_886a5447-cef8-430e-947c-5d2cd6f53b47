from decimal import Decimal

from business.enums import BusinessStatus, ChangeRequestStatus
from business.models import Business, BusinessChangeRequest, BusinessVASProduct
from business.v1.filters import BusinessFilter
from business.v1.serializers import (
    BusinessChangeRequestDetailSerializer,
    BusinessChangeRequestMinimalSerializer,
    BusinessInformationSerializer,
    BusinessListSerializer,
    BusinessStatusUpdateSerializer,
    DirectorListSerializer,
    DocumentMiniSerializer,
    SettlementDetailsSerializer,
)
from common.pagination import LargeDatasetKeySetPagination
from common.permissions import IsAdmin
from console.v1.filters import AdminBusinessChangeRequestFilter
from console.v1.serializers import (
    BusinessChangeRequestApproveSerializer,
    BusinessChangeRequestOverviewSerializer,
    BusinessChangeRequestRejectSerializer,
    BusinessOverviewSerializer,
    VASProductActivationSerializer,
    VASProductDeactivationSerializer,
    VASProductStatusResponseSerializer,
)
from django.db.models import Q, Sum
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum
from wallet.enums import WalletEnums
from wallet.v1.serializers import WalletSerializer


class AdminBusinessManagementViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAdmin]
    queryset = (
        Business.objects.select_related("owner")
        .annotate(
            general_wallet_balance=Sum(
                "wallets__balance", filter=Q(wallets__type=WalletEnums.GENERAL)
            )
        )
        .all()
        .order_by("-created_at")
    )
    serializer_class = BusinessListSerializer
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
    ]
    filterset_class = BusinessFilter
    search_fields = ["name", "email", "owner__email", "owner__fullname"]
    ordering_fields = ["-created_at"]
    pagination_class = LargeDatasetKeySetPagination

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(
        methods=["GET"],
        detail=False,
        url_path="overview",
        serializer_class=BusinessOverviewSerializer,
    )
    def fetch_business_overview(self, request, pk=None):
        total_businesses = Business.objects.count()
        total_active_businesses = Business.objects.filter(
            status=BusinessStatus.Active.value
        ).count()
        total_inactive_businesses = Business.objects.filter(
            status=BusinessStatus.Inactive.value
        ).count()
        total_verified_businesses = Business.objects.filter(
            status=BusinessStatus.Verified.value
        ).count()

        overview_data = {
            "total": total_businesses,
            "total_active": total_active_businesses,
            "total_inactive": total_inactive_businesses,
            "total_verified": total_verified_businesses,
        }
        data = self.get_serializer(overview_data).data
        return Response(
            data,
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=True,
        url_path="set-status",
        serializer_class=BusinessStatusUpdateSerializer,
    )
    def set_business_status(self, request, pk=None):
        business: Business = self.get_object()
        serializer = self.get_serializer(instance=business, data=request.data)
        serializer.is_valid(raise_exception=True)
        updated_business_instance = serializer.save()
        response_data = serializer.data
        if (
            hasattr(updated_business_instance, "_status_changed_for_response")
            and updated_business_instance._status_changed_for_response
        ):
            http_status = status.HTTP_200_OK
        else:
            http_status = status.HTTP_409_CONFLICT
        return Response(response_data, status=http_status)

    @action(
        methods=["GET"],
        detail=True,
        url_path="fetch-information",
        serializer_class=BusinessInformationSerializer,
    )
    def fetch_business_information(self, request, pk=None):
        business: Business = self.get_object()
        data = self.serializer_class(business).data
        return Response(
            data,
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=True,
        url_path="wallets",
        serializer_class=WalletSerializer,
    )
    def fetch_business_wallets(self, request, pk=None):
        business: Business = self.get_object()
        all_wallets = business.wallets.all()
        individual_wallets_data = self.serializer_class(all_wallets, many=True).data

        VIRTUAL_ACCOUNT_TYPES = [
            WalletEnums.WEMA_VIRTUAL_ACCOUNT,
            WalletEnums.KOLOMONI_VIRTUAL_ACCOUNT,
            WalletEnums.ACCESS_VIRTUAL_ACCOUNT,
        ]

        virtual_wallets_sum = business.wallets.filter(
            Q(type__in=VIRTUAL_ACCOUNT_TYPES)
        ).aggregate(total_virtual_balance=Sum("balance"))

        total_virtual_balance = virtual_wallets_sum.get(
            "total_virtual_balance", Decimal("0.00")
        )
        total_virtual_balance_str = str(total_virtual_balance)

        response_data = {
            "virtual_account_wallets_balance": total_virtual_balance_str,
            "all_wallets": individual_wallets_data,
        }

        return Response(
            response_data,
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=True,
        url_path="directors",
        serializer_class=DirectorListSerializer,
    )
    def fetch_directors(self, request, pk=None):
        business: Business = self.get_object()
        directors = business.directors.all()
        data = self.serializer_class(directors, many=True).data
        return Response(
            data,
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=True,
        url_path="settlement-details",
        serializer_class=SettlementDetailsSerializer,
    )
    def fetch_settlement_details(self, request, pk=None):
        business: Business = self.get_object()
        settlement_details = business.settlement_details.all()
        data = SettlementDetailsSerializer(settlement_details, many=True).data
        return Response(
            data,
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=True,
        url_path="documents",
        serializer_class=DocumentMiniSerializer,
    )
    def fetch_documents(self, request, pk=None):
        business: Business = self.get_object()
        documents = business.documents.all()
        data = DocumentMiniSerializer(documents, many=True).data
        return Response(
            data,
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=True,
        url_path="activate-vas",
        serializer_class=VASProductActivationSerializer,
    )
    def activate_vas_product(self, request, pk=None):
        business = self.get_object()
        serializer = self.get_serializer(
            data=request.data, context={"business": business, "admin": request.user}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"message": "VAS product activated successfully."},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=True,
        url_path="deactivate-vas",
        serializer_class=VASProductDeactivationSerializer,
    )
    def deactivate_vas_product(self, request, pk=None):
        business = self.get_object()
        serializer = self.get_serializer(
            data=request.data, context={"business": business, "admin": request.user}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"message": "VAS product deactivated successfully."},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=True,
        url_path="vas-status",
        serializer_class=VASProductStatusResponseSerializer,
    )
    def vas_product_status(self, request, pk=None):
        business = self.get_object()
        all_types = TransactionClassEnum.values()
        product_map = {
            p.product_type: p
            for p in BusinessVASProduct.objects.filter(business=business)
        }

        status_data = []

        for product_type in all_types:
            product: BusinessVASProduct = product_map.get(product_type)
            status_data.append(
                {
                    "product_type": product_type,
                    "is_active": product.is_active if product else False,
                    "activated_at": product.activated_at if product else None,
                    "deactivated_at": product.deactivated_at if product else None,
                }
            )

        response_data = {
            "business_id": str(business.id),
            "business_name": business.name,
            "vas_products": status_data,
        }

        serializer = self.get_serializer(response_data)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AdminBusinessChangeRequestViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAdmin]
    queryset = BusinessChangeRequest.objects.all().order_by("-created_at")
    serializer_class = BusinessChangeRequestMinimalSerializer
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
    ]
    search_fields = [
        "business__name",
        "business__email",
        "business__phone",
        "business__owner__email",
    ]
    ordering_fields = ["-created_at"]
    filterset_class = AdminBusinessChangeRequestFilter
    pagination_class = LargeDatasetKeySetPagination

    def get_serializer_class(self):
        if self.action == "retrieve":
            return BusinessChangeRequestDetailSerializer
        return super().get_serializer_class()

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(
        methods=["GET"],
        detail=False,
        url_path="overview",
        serializer_class=BusinessChangeRequestOverviewSerializer,
    )
    def fetch_change_request_overview(self, request, pk=None):
        total_requests = BusinessChangeRequest.objects.count()
        total_pending_requests = BusinessChangeRequest.objects.filter(
            status=ChangeRequestStatus.Pending.value
        ).count()
        total_approved_requests = BusinessChangeRequest.objects.filter(
            status=ChangeRequestStatus.Approved.value
        ).count()
        total_rejected_requests = BusinessChangeRequest.objects.filter(
            status=ChangeRequestStatus.Rejected.value
        ).count()
        overview_data = {
            "total": total_requests,
            "total_pending": total_pending_requests,
            "total_approved": total_approved_requests,
            "total_rejected": total_rejected_requests,
        }
        data = self.get_serializer(overview_data).data
        return Response(
            data,
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=True,
        url_path="approve",
        serializer_class=BusinessChangeRequestApproveSerializer,
    )
    def approve_change_request(self, request, pk=None):
        change_request = self.get_object()
        serializer = self.get_serializer(
            change_request, data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"message": "Business change request approved successfully."},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=True,
        url_path="reject",
        serializer_class=BusinessChangeRequestRejectSerializer,
    )
    def reject_change_request(self, request, pk=None):
        change_request = self.get_object()
        serializer = self.get_serializer(
            change_request, data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"message": "Business change request rejected successfully."},
            status=status.HTTP_200_OK,
        )
