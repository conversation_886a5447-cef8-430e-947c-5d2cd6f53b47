import logging

from business.handlers.product_activation import (
    BaseVASProductHandler,
    VASProductHandlerFactory,
)
from business.models import BusinessChangeRequest, BusinessVASProduct
from django.db import transaction
from rest_framework import serializers
from transaction.enums import TransactionClassEnum

logger = logging.getLogger(__name__)


class VASProductActivationSerializer(serializers.Serializer):
    product_type = serializers.ChoiceField(choices=TransactionClassEnum.choices())
    notes = serializers.CharField(required=False, allow_blank=True)

    def validate(self, attrs):
        business = self.context["business"]
        product_type = attrs["product_type"]

        if BusinessVASProduct.objects.filter(
            business=business, product_type=product_type, is_active=True
        ).exists():
            raise serializers.ValidationError(
                {"product_type": "This product is already active for the business."}
            )

        return attrs

    @transaction.atomic
    def create(self, validated_data):
        business = self.context["business"]
        admin = self.context["admin"]
        product_type = validated_data["product_type"]
        notes = validated_data.get("notes")

        handler: BaseVASProductHandler = VASProductHandlerFactory.get_handler(
            product_type
        )
        handler.activate(business, admin, notes)

        return {"product_type": product_type}


class VASProductDeactivationSerializer(serializers.Serializer):
    product_type = serializers.ChoiceField(choices=TransactionClassEnum.choices())
    notes = serializers.CharField(required=False, allow_blank=True)

    def validate(self, attrs):
        business = self.context["business"]
        product_type = attrs["product_type"]

        try:
            self.instance = BusinessVASProduct.objects.get(
                business=business, product_type=product_type, is_active=True
            )
        except BusinessVASProduct.DoesNotExist:
            raise serializers.ValidationError(
                {
                    "product_type": "This product is not currently active for this business."
                }
            )

        return attrs

    @transaction.atomic
    def update(self, instance: BusinessVASProduct, validated_data):
        admin = self.context["admin"]
        notes = validated_data.get("notes")

        handler: BaseVASProductHandler = VASProductHandlerFactory.get_handler(
            instance.product_type
        )
        handler.deactivate(instance.business, admin, notes)

        return instance


class VASProductStatusSerializer(serializers.Serializer):
    product_type = serializers.CharField()
    is_active = serializers.BooleanField()
    activated_at = serializers.DateTimeField(allow_null=True)
    deactivated_at = serializers.DateTimeField(allow_null=True)


class VASProductStatusResponseSerializer(serializers.Serializer):
    business_id = serializers.CharField()
    business_name = serializers.CharField()
    vas_products = VASProductStatusSerializer(many=True)


class BusinessOverviewSerializer(serializers.Serializer):
    total = serializers.IntegerField()
    total_active = serializers.IntegerField()
    total_inactive = serializers.IntegerField()
    total_verified = serializers.IntegerField()


class BusinessChangeRequestOverviewSerializer(serializers.Serializer):
    total = serializers.IntegerField()
    total_pending = serializers.IntegerField()
    total_approved = serializers.IntegerField()
    total_rejected = serializers.IntegerField()


class BusinessChangeRequestApproveSerializer(serializers.ModelSerializer):
    class Meta:
        model = BusinessChangeRequest
        fields = []

    def update(self, instance: BusinessChangeRequest, validated_data):
        user = self.context["request"].user
        try:
            with transaction.atomic():
                instance.approve(reviewed_by=user)
        except ValueError as e:
            raise serializers.ValidationError({"detail": str(e)})
        return instance


class BusinessChangeRequestRejectSerializer(serializers.ModelSerializer):
    rejection_note = serializers.CharField()

    class Meta:
        model = BusinessChangeRequest
        fields = ["rejection_note"]

    def update(self, instance: BusinessChangeRequest, validated_data):
        user = self.context["request"].user
        rejection_note = validated_data.get("rejection_note")
        try:
            with transaction.atomic():
                instance.reject(reviewed_by=user, note=rejection_note)
        except ValueError as e:
            raise serializers.ValidationError({"detail": str(e)})
        return instance
