# Generated by Django 5.1.7 on 2025-06-18 08:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ledger", "0003_alter_ledger_type"),
        ("transaction", "0021_transaction_vender_commissiontransaction"),
        ("wallet", "0003_alter_wallet_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="ledgertransaction",
            name="source_commission",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="source_commission_ledger_transactions",
                to="transaction.commissiontransaction",
            ),
        ),
        migrations.AlterField(
            model_name="ledger",
            name="type",
            field=models.CharField(
                choices=[
                    ("VIRTUAL_ACCOUNT_WEMA", "VIRTUAL_ACCOUNT_WEMA"),
                    ("VIRTUAL_ACCOUNT_KOLOMONI", "VIRTUAL_ACCOUNT_<PERSON>OLOMONI"),
                    ("TRANSFER", "TRANSFER"),
                    ("AIRTIME", "AIRTIME"),
                    ("DATA", "DATA"),
                    ("BETTING", "BETTING"),
                    ("ELECTRICITY", "ELECTRICITY"),
                    ("CABLE_TV", "CABLE_TV"),
                    ("SME_DATA", "SME_DATA"),
                    ("KYC", "KYC"),
                    ("EDUCATION", "EDUCATION"),
                    ("EPIN", "EPIN"),
                    ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                    ("AIRTIME_COMMISSION", "AIRTIME_COMMISSION"),
                    ("DATA_COMMISSION", "DATA_COMMISSION"),
                    ("EPIN_COMMISSION", "EPIN_COMMISSION"),
                    ("CABLE_TV_COMMISSION", "CABLE_TV_COMMISSION"),
                    ("EDUCATION_COMMISSION", "EDUCATION_COMMISSION"),
                    ("ELECTRIC_COMMISSION", "ELECTRIC_COMMISSION"),
                    ("BETTING_COMMISSION", "BETTING_COMMISSION"),
                ],
                db_index=True,
                max_length=30,
            ),
        ),
        migrations.AlterField(
            model_name="ledgertransaction",
            name="source_transaction",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="source_txn_ledger_transactions",
                to="transaction.transaction",
            ),
        ),
        migrations.AlterField(
            model_name="ledgertransaction",
            name="wallet",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="wallet_ledger_transactions",
                to="wallet.wallet",
            ),
        ),
    ]
