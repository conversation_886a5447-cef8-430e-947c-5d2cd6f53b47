# Generated by Django 5.1.7 on 2025-06-20 13:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("teams", "0003_alter_role_options_alter_role_name_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="role",
            name="type",
            field=models.CharField(
                choices=[("DEFAULT", "DEFAULT"), ("CUSTOM", "CUSTOM")],
                db_index=True,
                default="DEFAULT",
                help_text="Type of role (e.g., custom, default)",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="permission",
            name="feature",
            field=models.CharField(
                choices=[
                    ("dashboard_metrics", "dashboard_metrics"),
                    ("products", "products"),
                    ("transactions", "transactions"),
                    ("virtual_accounts", "virtual_accounts"),
                    ("virtual_accounts_withdrawal", "virtual_accounts_withdrawal"),
                    ("change_request", "change_request"),
                    ("teams", "teams"),
                    ("developer", "developer"),
                    ("security", "security"),
                    ("recurrent_debit_transaction", "recurrent_debit_transaction"),
                    ("recurrent_debit_mandate", "recurrent_debit_mandate"),
                    ("recurrent_debit_wallet", "recurrent_debit_wallet"),
                    ("dispute", "dispute"),
                    ("audit_logs", "audit_logs"),
                ],
                db_index=True,
                help_text="Feature this permission is associated with",
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="role",
            name="platform",
            field=models.CharField(
                choices=[("Admin", "Admin"), ("Business", "Business")],
                db_index=True,
                help_text="Platform for which this role is applicable (e.g., admin, business, global)",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="teammember",
            name="status",
            field=models.CharField(
                choices=[
                    ("Invited", "Invited"),
                    ("Active", "Active"),
                    ("Revoked", "Revoked"),
                ],
                db_index=True,
                default="Invited",
                max_length=20,
            ),
        ),
    ]
