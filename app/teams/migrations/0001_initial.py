# Generated by Django 5.1.7 on 2025-06-20 13:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("business", "0018_document_parent_document_alter_document_status"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Permission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("codename", models.CharField(max_length=100, unique=True)),
                (
                    "feature",
                    models.CharField(
                        choices=[
                            ("dashboard_metrics", "dashboard_metrics"),
                            ("products", "products"),
                            ("transactions", "transactions"),
                            ("virtual_accounts", "virtual_accounts"),
                            (
                                "virtual_accounts_withdrawal",
                                "virtual_accounts_withdrawal",
                            ),
                            ("change_request", "change_request"),
                            ("teams", "teams"),
                            ("developer", "developer"),
                            ("security", "security"),
                            (
                                "recurrent_debit_transaction",
                                "recurrent_debit_transaction",
                            ),
                            ("recurrent_debit_mandate", "recurrent_debit_mandate"),
                            ("recurrent_debit_wallet", "recurrent_debit_wallet"),
                            ("dispute", "dispute"),
                            ("audit_logs", "audit_logs"),
                        ],
                        max_length=100,
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("CREATE", "CREATE"),
                            ("READ", "READ"),
                            ("UPDATE", "UPDATE"),
                        ],
                        max_length=50,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Role",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                (
                    "platform",
                    models.CharField(
                        choices=[("Admin", "Admin"), ("Business", "Business")],
                        help_text="Platform for which this role is applicable (e.g., admin, business, global)",
                        max_length=20,
                    ),
                ),
                (
                    "permissions",
                    models.ManyToManyField(related_name="roles", to="teams.permission"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="AdminProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="admin_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="admin_users",
                        to="teams.role",
                    ),
                ),
            ],
            options={
                "verbose_name": "Admin Profile",
                "verbose_name_plural": "Admin Profiles",
            },
        ),
        migrations.CreateModel(
            name="TeamMember",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Invited", "Invited"),
                            ("Active", "Active"),
                            ("Revoked", "Revoked"),
                        ],
                        default="Invited",
                        max_length=20,
                    ),
                ),
                ("joined_at", models.DateTimeField(auto_now_add=True)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="core_team_members",
                        to="business.business",
                    ),
                ),
                (
                    "invited_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="invited_team_members",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="team_members",
                        to="teams.role",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="teammember",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Team Member",
                "verbose_name_plural": "Team Members",
            },
        ),
    ]
