# Generated by Django 5.1.7 on 2025-06-20 13:08

import common.kgs
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("teams", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="adminprofile",
            name="created_at",
            field=models.DateTimeField(
                db_index=True, default=django.utils.timezone.now
            ),
        ),
        migrations.AddField(
            model_name="adminprofile",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="permission",
            name="created_at",
            field=models.DateTimeField(
                db_index=True, default=django.utils.timezone.now
            ),
        ),
        migrations.AddField(
            model_name="permission",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="role",
            name="created_at",
            field=models.DateTimeField(
                db_index=True, default=django.utils.timezone.now
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="teammember",
            name="created_at",
            field=models.DateTimeField(
                db_index=True, default=django.utils.timezone.now
            ),
        ),
        migrations.AddField(
            model_name="teammember",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="adminprofile",
            name="id",
            field=models.CharField(
                db_index=True,
                default=common.kgs.generate_unique_id,
                editable=False,
                max_length=50,
                primary_key=True,
                serialize=False,
            ),
        ),
        migrations.AlterField(
            model_name="permission",
            name="id",
            field=models.CharField(
                db_index=True,
                default=common.kgs.generate_unique_id,
                editable=False,
                max_length=50,
                primary_key=True,
                serialize=False,
            ),
        ),
        migrations.AlterField(
            model_name="role",
            name="id",
            field=models.CharField(
                db_index=True,
                default=common.kgs.generate_unique_id,
                editable=False,
                max_length=50,
                primary_key=True,
                serialize=False,
            ),
        ),
        migrations.AlterField(
            model_name="teammember",
            name="id",
            field=models.CharField(
                db_index=True,
                default=common.kgs.generate_unique_id,
                editable=False,
                max_length=50,
                primary_key=True,
                serialize=False,
            ),
        ),
    ]
