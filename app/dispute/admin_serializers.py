from django.utils import timezone
from rest_framework import serializers

from .admin_permissions import can_user_be_assigned_disputes
from .enums import DisputeStatus
from .models import AdminTeamMember, Dispute, DisputeAssignment, DisputeResponse


class AdminTeamMemberSerializer(serializers.ModelSerializer):
    """Serializer for admin team members."""

    user_email = serializers.EmailField(source="user.email", read_only=True)
    user_name = serializers.CharField(source="user.fullname", read_only=True)
    added_by_name = serializers.CharField(source="added_by.fullname", read_only=True)
    can_be_assigned = serializers.SerializerMethodField()

    class Meta:
        model = AdminTeamMember
        fields = [
            "id",
            "user_email",
            "user_name",
            "role",
            "is_active",
            "added_by_name",
            "can_be_assigned",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_can_be_assigned(self, obj):
        """Check if this team member can be assigned disputes."""
        return can_user_be_assigned_disputes(obj.user)


class AdminDisputeListSerializer(serializers.ModelSerializer):
    """Serializer for listing disputes in admin panel."""

    business_name = serializers.CharField(source="business.name", read_only=True)
    created_by_name = serializers.CharField(
        source="created_by.fullname", read_only=True
    )
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    vas_service_display = serializers.CharField(
        source="get_vas_service_display", read_only=True
    )
    assigned_to_email = serializers.SerializerMethodField()
    assigned_to_name = serializers.SerializerMethodField()
    response_count = serializers.SerializerMethodField()

    class Meta:
        model = Dispute
        fields = [
            "id",
            "transaction_reference",
            "business_name",
            "merchant_name",
            "vas_service",
            "vas_service_display",
            "amount",
            "status",
            "status_display",
            "created_by_name",
            "assigned_to_email",
            "assigned_to_name",
            "response_count",
            "created_at",
            "updated_at",
        ]

    def get_assigned_to_email(self, obj):
        """Get email of assigned team member."""
        active_assignment = obj.assignments.filter(is_active=True).first()
        return active_assignment.assigned_to.email if active_assignment else None

    def get_assigned_to_name(self, obj):
        """Get name of assigned team member."""
        active_assignment = obj.assignments.filter(is_active=True).first()
        return active_assignment.assigned_to.fullname if active_assignment else None

    def get_response_count(self, obj):
        """Get count of responses to this dispute."""
        return obj.responses.count()


class AdminDisputeDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed dispute view in admin panel."""

    business_name = serializers.CharField(source="business.name", read_only=True)
    business_email = serializers.EmailField(source="business.email", read_only=True)
    created_by_name = serializers.CharField(
        source="created_by.fullname", read_only=True
    )
    created_by_email = serializers.EmailField(source="created_by.email", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    vas_service_display = serializers.CharField(
        source="get_vas_service_display", read_only=True
    )
    assigned_to_email = serializers.SerializerMethodField()
    assigned_to_name = serializers.SerializerMethodField()
    assignment_notes = serializers.SerializerMethodField()
    responses = serializers.SerializerMethodField()

    class Meta:
        model = Dispute
        fields = [
            "id",
            "transaction_reference",
            "business_name",
            "business_email",
            "merchant_name",
            "vas_service",
            "vas_service_display",
            "amount",
            "charge",
            "stamp_duty",
            "previous_balance",
            "new_balance",
            "transaction_date",
            "status",
            "status_display",
            "message",
            "created_by_name",
            "created_by_email",
            "assigned_to_email",
            "assigned_to_name",
            "assignment_notes",
            "responses",
            "resolved_at",
            "resolution_notes",
            "created_at",
            "updated_at",
        ]

    def get_assigned_to_email(self, obj):
        """Get email of assigned team member."""
        active_assignment = obj.assignments.filter(is_active=True).first()
        return active_assignment.assigned_to.email if active_assignment else None

    def get_assigned_to_name(self, obj):
        """Get name of assigned team member."""
        active_assignment = obj.assignments.filter(is_active=True).first()
        return active_assignment.assigned_to.fullname if active_assignment else None

    def get_assignment_notes(self, obj):
        """Get assignment notes."""
        active_assignment = obj.assignments.filter(is_active=True).first()
        return active_assignment.notes if active_assignment else None

    def get_responses(self, obj):
        """Get all responses to this dispute."""
        responses = obj.responses.all().order_by("created_at")
        return DisputeResponseSerializer(responses, many=True).data


class DisputeAssignmentSerializer(serializers.ModelSerializer):
    """Serializer for dispute assignments."""

    assigned_to_email = serializers.EmailField(write_only=True)
    assigned_to_name = serializers.CharField(
        source="assigned_to.fullname", read_only=True
    )
    assigned_by_name = serializers.CharField(
        source="assigned_by.fullname", read_only=True
    )
    dispute_reference = serializers.CharField(
        source="dispute.transaction_reference", read_only=True
    )

    class Meta:
        model = DisputeAssignment
        fields = [
            "id",
            "assigned_to_email",
            "assigned_to_name",
            "assigned_by_name",
            "dispute_reference",
            "notes",
            "is_active",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]

    def validate_assigned_to_email(self, value):
        """Validate that the assignee exists and can be assigned disputes."""
        from user.models import User

        try:
            user = User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("User with this email does not exist.")

        if not can_user_be_assigned_disputes(user):
            raise serializers.ValidationError(
                "This user cannot be assigned disputes. They must be an admin team member with update permissions."
            )

        return value

    def create(self, validated_data):
        """Create dispute assignment."""
        from user.models import User

        assigned_to_email = validated_data.pop("assigned_to_email")
        assigned_to = User.objects.get(email=assigned_to_email)

        # Deactivate any existing assignments for this dispute
        DisputeAssignment.objects.filter(
            dispute=validated_data["dispute"], is_active=True
        ).update(is_active=False)

        # Create new assignment
        assignment = DisputeAssignment.objects.create(
            assigned_to=assigned_to, **validated_data
        )

        return assignment


class DisputeResponseSerializer(serializers.ModelSerializer):
    """Serializer for dispute responses."""

    responder_name = serializers.CharField(source="responder.fullname", read_only=True)
    responder_email = serializers.EmailField(source="responder.email", read_only=True)
    previous_status_display = serializers.CharField(
        source="get_previous_status_display", read_only=True
    )
    new_status_display = serializers.CharField(
        source="get_new_status_display", read_only=True
    )

    class Meta:
        model = DisputeResponse
        fields = [
            "id",
            "message",
            "previous_status",
            "previous_status_display",
            "new_status",
            "new_status_display",
            "is_internal_note",
            "responder_name",
            "responder_email",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class DisputeResponseCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating dispute responses."""

    class Meta:
        model = DisputeResponse
        fields = ["message", "new_status", "is_internal_note"]

    def validate_new_status(self, value):
        """Validate status transition."""
        dispute = self.context.get("dispute")
        if dispute:
            current_status = dispute.status

            # Define valid status transitions
            valid_transitions = {
                DisputeStatus.PENDING.value: [DisputeStatus.IN_REVIEW.value],
                DisputeStatus.IN_REVIEW.value: [
                    DisputeStatus.RESOLVED.value,
                    DisputeStatus.PENDING.value,
                ],
                DisputeStatus.RESOLVED.value: [],  # No transitions from resolved
            }

            if value not in valid_transitions.get(current_status, []):
                raise serializers.ValidationError(
                    f"Cannot transition from {current_status} to {value}"
                )

        return value

    def create(self, validated_data):
        """Create dispute response and update dispute status."""
        dispute = self.context["dispute"]
        responder = self.context["request"].user

        # Create response
        response = DisputeResponse.objects.create(
            dispute=dispute,
            responder=responder,
            previous_status=dispute.status,
            **validated_data,
        )

        # Update dispute status
        dispute.status = validated_data["new_status"]
        if validated_data["new_status"] == DisputeStatus.RESOLVED.value:
            dispute.resolved_by = responder
            dispute.resolved_at = timezone.now()

        dispute.save(update_fields=["status", "resolved_by", "resolved_at"])

        return response


class AdminDisputeStatsSerializer(serializers.Serializer):
    """Serializer for admin dispute statistics."""

    total_disputes = serializers.IntegerField()
    pending_disputes = serializers.IntegerField()
    in_review_disputes = serializers.IntegerField()
    resolved_disputes = serializers.IntegerField()
    assigned_disputes = serializers.IntegerField()
    unassigned_disputes = serializers.IntegerField()
