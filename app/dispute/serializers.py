from decimal import Decimal

from django.utils import timezone
from rest_framework import serializers
from transaction.enums import TransactionClassEnum

from .enums import DisputeStatus
from .models import BusinessMember, Dispute
from .permissions import get_user_business_context
from .services import DisputeCreationService


class BusinessMemberSerializer(serializers.ModelSerializer):
    """Serializer for business team members."""

    user_email = serializers.EmailField(source="user.email", read_only=True)
    user_name = serializers.CharField(source="user.fullname", read_only=True)
    added_by_name = serializers.CharField(source="added_by.fullname", read_only=True)

    class Meta:
        model = BusinessMember
        fields = [
            "id",
            "user_email",
            "user_name",
            "role",
            "is_active",
            "added_by_name",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class DisputeCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating disputes."""

    class Meta:
        model = Dispute
        fields = [
            "transaction_reference",
            "vas_service",
            "amount",
            "charge",
            "stamp_duty",
            "previous_balance",
            "new_balance",
            "transaction_date",
            "merchant_name",
            "message",
        ]

    def validate_vas_service(self, value):
        """Validate VAS service type."""
        valid_services = [choice[0] for choice in TransactionClassEnum.choices()]
        if value not in valid_services:
            raise serializers.ValidationError(
                f"Invalid VAS service. Must be one of: {', '.join(valid_services)}"
            )
        return value

    def validate_amount(self, value):
        """Validate transaction amount."""
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than zero.")
        return value

    def validate(self, attrs):
        """Validate the entire dispute data."""
        # Ensure new_balance calculation is consistent
        expected_new_balance = (
            attrs["previous_balance"]
            - attrs["amount"]
            - attrs["charge"]
            - attrs["stamp_duty"]
        )

        if abs(attrs["new_balance"] - expected_new_balance) > Decimal("0.01"):
            raise serializers.ValidationError(
                "New balance calculation doesn't match the provided values."
            )

        return attrs

    def create(self, validated_data):
        """Create a new dispute."""
        request = self.context.get("request")
        business, role = get_user_business_context(request.user)

        if not business:
            raise serializers.ValidationError(
                "User is not associated with any business."
            )

        validated_data["business"] = business
        validated_data["created_by"] = request.user
        validated_data["status"] = DisputeStatus.PENDING.value

        return super().create(validated_data)


class DisputeCreateFromReferenceSerializer(serializers.Serializer):
    """Serializer for creating disputes using only transaction reference and message."""

    transaction_reference = serializers.CharField(
        max_length=100, help_text="Transaction reference ID to create dispute for"
    )
    message = serializers.CharField(help_text="Description of the dispute/issue")

    def validate_transaction_reference(self, value):
        """Validate that transaction reference exists and belongs to user's business."""
        request = self.context.get("request")
        if not request or not request.user:
            raise serializers.ValidationError("Authentication required.")

        # Get user's business context
        business, _ = get_user_business_context(request.user)
        if not business:
            raise serializers.ValidationError(
                "User is not associated with any business."
            )

        # Validate transaction exists
        try:
            transaction_info = DisputeCreationService.validate_transaction_reference(
                value, business
            )
            # Store transaction info for use in create method
            self._transaction_info = transaction_info
            return value
        except Exception as e:
            raise serializers.ValidationError(str(e))

    def validate_message(self, value):
        """Validate message content."""
        if len(value.strip()) < 10:
            raise serializers.ValidationError(
                "Message must be at least 10 characters long."
            )
        return value.strip()

    def create(self, validated_data):
        """Create dispute from transaction reference."""
        request = self.context.get("request")

        try:
            # Create dispute data from transaction reference
            dispute_data = DisputeCreationService.create_dispute_from_reference(
                reference=validated_data["transaction_reference"],
                message=validated_data["message"],
                user=request.user,
            )

            # Create the dispute
            dispute = Dispute.objects.create(**dispute_data)
            return dispute

        except Exception as e:
            raise serializers.ValidationError(f"Failed to create dispute: {str(e)}")

    def to_representation(self, instance):
        """Return detailed dispute representation."""
        return DisputeDetailSerializer(instance, context=self.context).data


class DisputeListSerializer(serializers.ModelSerializer):
    """Serializer for listing disputes."""

    created_by_name = serializers.CharField(
        source="created_by.fullname", read_only=True
    )
    business_name = serializers.CharField(source="business.name", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    vas_service_display = serializers.CharField(
        source="get_vas_service_display", read_only=True
    )

    class Meta:
        model = Dispute
        fields = [
            "id",
            "transaction_reference",
            "vas_service",
            "vas_service_display",
            "amount",
            "status",
            "status_display",
            "merchant_name",
            "message",
            "created_by_name",
            "business_name",
            "created_at",
            "updated_at",
        ]


class DisputeDetailSerializer(serializers.ModelSerializer):
    """Serializer for dispute details with status-based timeline information."""

    created_by_name = serializers.CharField(
        source="created_by.fullname", read_only=True
    )
    created_by_email = serializers.EmailField(source="created_by.email", read_only=True)
    business_name = serializers.CharField(source="business.name", read_only=True)
    resolved_by_name = serializers.CharField(
        source="resolved_by.fullname", read_only=True
    )
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    vas_service_display = serializers.CharField(
        source="get_vas_service_display", read_only=True
    )
    timeline = serializers.SerializerMethodField()

    class Meta:
        model = Dispute
        fields = [
            "id",
            "transaction_reference",
            "vas_service",
            "vas_service_display",
            "amount",
            "charge",
            "stamp_duty",
            "previous_balance",
            "new_balance",
            "transaction_date",
            "status",
            "status_display",
            "merchant_name",
            "message",
            "created_by_name",
            "created_by_email",
            "business_name",
            "resolved_by_name",
            "resolved_at",
            "resolution_notes",
            "created_at",
            "updated_at",
            "timeline",
        ]

    def get_timeline(self, obj):
        """
        Get timeline information based on dispute status.

        - PENDING: creation date/time only
        - IN_REVIEW: creation date/time + notes from review phase
        - RESOLVED: creation date/time + all notes + resolution info
        """
        timeline = {
            "created_at": obj.created_at,
            "status": obj.status,
        }

        # Get all responses (notes) for this dispute, excluding internal notes
        responses = obj.responses.filter(is_internal_note=False).order_by("created_at")

        if obj.status == DisputeStatus.PENDING.value:
            # For pending disputes, only show creation time
            timeline["notes"] = []

        elif obj.status == DisputeStatus.IN_REVIEW.value:
            # For in-review disputes, show creation time + any notes added during review
            timeline["notes"] = [
                {
                    "message": response.message,
                    "created_at": response.created_at,
                    "responder_name": response.responder.fullname,
                    "status_when_added": response.new_status,
                }
                for response in responses
            ]

        elif obj.status == DisputeStatus.RESOLVED.value:

            timeline["notes"] = [
                {
                    "message": response.message,
                    "created_at": response.created_at,
                    "responder_name": response.responder.fullname,
                    "status_when_added": response.new_status,
                }
                for response in responses
            ]
            timeline["resolved_at"] = obj.resolved_at
            timeline["resolved_by"] = (
                obj.resolved_by.fullname if obj.resolved_by else None
            )
            timeline["resolution_notes"] = obj.resolution_notes

        return timeline


class DisputeUpdateStatusSerializer(serializers.ModelSerializer):
    """Serializer for updating dispute status."""

    class Meta:
        model = Dispute
        fields = ["status", "resolution_notes"]

    def validate_status(self, value):
        """Validate status transition."""
        if self.instance:
            current_status = self.instance.status

            # Define valid status transitions
            valid_transitions = {
                DisputeStatus.PENDING.value: [DisputeStatus.IN_REVIEW.value],
                DisputeStatus.IN_REVIEW.value: [
                    DisputeStatus.RESOLVED.value,
                    DisputeStatus.PENDING.value,
                ],
                DisputeStatus.RESOLVED.value: [],  # No transitions from resolved
            }

            if value not in valid_transitions.get(current_status, []):
                raise serializers.ValidationError(
                    f"Cannot transition from {current_status} to {value}"
                )

        return value

    def update(self, instance, validated_data):
        """Update dispute status."""
        if (
            "status" in validated_data
            and validated_data["status"] == DisputeStatus.RESOLVED.value
        ):
            validated_data["resolved_by"] = self.context["request"].user
            validated_data["resolved_at"] = timezone.now()

        return super().update(instance, validated_data)


class DisputeStatsSerializer(serializers.Serializer):
    """Serializer for dispute statistics."""

    total_disputes = serializers.IntegerField()
    pending_disputes = serializers.IntegerField()
    in_review_disputes = serializers.IntegerField()
    resolved_disputes = serializers.IntegerField()
    business_name = serializers.CharField()
    business_id = serializers.CharField()
