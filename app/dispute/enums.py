from common.enums import CustomEnum


class DisputeStatus(CustomEnum):
    PENDING = "PENDING"
    IN_REVIEW = "IN_REVIEW"
    RESOLVED = "RESOLVED"


class BusinessRole(CustomEnum):
    BUSINESS_OWNER = "BUSINESS_OWNER"
    MERCHANT_ADMIN = "MERCHANT_ADMIN"
    CUSTOMER_SUPPORT = "CUSTOMER_SUPPORT"
    OPERATIONS = "OPERATIONS"
    RECONCILIATION = "RECONCILIATION"
    DEVELOPER = "DEVELOPER"


class AdminRole(CustomEnum):
    """Admin team roles for dispute management"""

    ADMIN = "ADMIN"
    CUSTOMER_SUPPORT = "CUSTOMER_SUPPORT"
    OPERATIONS = "OPERATIONS"
    RECONCILIATION = "RECONCILIATION"
    DEVELOPER = "DEVELOPER"


class DisputePermission(CustomEnum):
    CREATE_DISPUTE = "CREATE_DISPUTE"
    READ_DISPUTE = "READ_DISPUTE"
    READ_ALL_BUSINESS_DISPUTES = "READ_ALL_BUSINESS_DISPUTES"


class AdminDisputePermission(CustomEnum):
    """Admin permissions for dispute management"""

    READ_ALL_DISPUTES = "READ_ALL_DISPUTES"
    UPDATE_DISPUTES = "UPDATE_DISPUTES"
    ASSIGN_DISPUTES = "ASSIGN_DISPUTES"


# Role-based permissions mapping
ROLE_PERMISSIONS = {
    BusinessRole.BUSINESS_OWNER: [
        DisputePermission.CREATE_DISPUTE,
        DisputePermission.READ_DISPUTE,
        DisputePermission.READ_ALL_BUSINESS_DISPUTES,
    ],
    BusinessRole.MERCHANT_ADMIN: [
        DisputePermission.CREATE_DISPUTE,
        DisputePermission.READ_ALL_BUSINESS_DISPUTES,
    ],
    BusinessRole.CUSTOMER_SUPPORT: [
        DisputePermission.READ_ALL_BUSINESS_DISPUTES,
    ],
    BusinessRole.OPERATIONS: [
        DisputePermission.CREATE_DISPUTE,
        DisputePermission.READ_ALL_BUSINESS_DISPUTES,
    ],
    BusinessRole.RECONCILIATION: [],
    BusinessRole.DEVELOPER: [],
}

# Admin role-based permissions mapping
ADMIN_ROLE_PERMISSIONS = {
    AdminRole.ADMIN: [
        AdminDisputePermission.READ_ALL_DISPUTES,
        AdminDisputePermission.UPDATE_DISPUTES,
        AdminDisputePermission.ASSIGN_DISPUTES,
    ],
    AdminRole.CUSTOMER_SUPPORT: [
        AdminDisputePermission.READ_ALL_DISPUTES,
    ],
    AdminRole.OPERATIONS: [
        AdminDisputePermission.READ_ALL_DISPUTES,
        AdminDisputePermission.UPDATE_DISPUTES,
    ],
    AdminRole.RECONCILIATION: [],
    AdminRole.DEVELOPER: [],
}
