# Generated by Django 5.1.7 on 2025-06-18 09:02

from decimal import Decimal

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("business", "0017_alter_businesschangerequest_options"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BusinessMember",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("BUSINESS_OWNER", "BUSINESS_OWNER"),
                            ("MERCHANT_ADMIN", "MERCHANT_ADMIN"),
                            ("CUSTOMER_SUPPORT", "CUSTOMER_SUPPORT"),
                            ("OPERATIONS", "OPERATIONS"),
                            ("RECONCILIATION", "RECONCILIATION"),
                            ("DEVELOPER", "DEVELOPER"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("is_active", models.BooleanField(db_index=True, default=True)),
                (
                    "added_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="added_team_members",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="team_members",
                        to="business.business",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="business_memberships",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "unique_together": {("business", "user")},
            },
        ),
        migrations.CreateModel(
            name="Dispute",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "transaction_reference",
                    models.CharField(
                        db_index=True,
                        help_text="Reference ID of the disputed transaction",
                        max_length=100,
                    ),
                ),
                (
                    "vas_service",
                    models.CharField(
                        choices=[
                            ("VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"),
                            ("TRANSFER", "TRANSFER"),
                            ("AIRTIME", "AIRTIME"),
                            ("DATA", "DATA"),
                            ("BETTING", "BETTING"),
                            ("ELECTRICITY", "ELECTRICITY"),
                            ("CABLE_TV", "CABLE_TV"),
                            ("SME_DATA", "SME_DATA"),
                            ("KYC", "KYC"),
                            ("EDUCATION", "EDUCATION"),
                            ("EPIN", "EPIN"),
                            ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                        ],
                        db_index=True,
                        help_text="VAS service type (e.g., Airtime, Electricity)",
                        max_length=30,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, help_text="Transaction amount", max_digits=20
                    ),
                ),
                (
                    "charge",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Transaction charge/fee",
                        max_digits=20,
                    ),
                ),
                (
                    "stamp_duty",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Stamp duty charged",
                        max_digits=20,
                    ),
                ),
                (
                    "previous_balance",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Wallet balance before transaction",
                        max_digits=20,
                    ),
                ),
                (
                    "new_balance",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Wallet balance after transaction",
                        max_digits=20,
                    ),
                ),
                (
                    "transaction_date",
                    models.DateTimeField(
                        help_text="Date and time when the transaction occurred"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "PENDING"),
                            ("IN_REVIEW", "IN_REVIEW"),
                            ("RESOLVED", "RESOLVED"),
                        ],
                        db_index=True,
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                (
                    "merchant_name",
                    models.CharField(
                        help_text="Name of the business owner/merchant", max_length=255
                    ),
                ),
                (
                    "message",
                    models.TextField(help_text="Description of the dispute/issue"),
                ),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("resolution_notes", models.TextField(blank=True, null=True)),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="disputes",
                        to="business.business",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_disputes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_disputes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["business", "status"],
                        name="dispute_dis_busines_f3a38d_idx",
                    ),
                    models.Index(
                        fields=["transaction_reference"],
                        name="dispute_dis_transac_71d620_idx",
                    ),
                    models.Index(
                        fields=["vas_service", "status"],
                        name="dispute_dis_vas_ser_2815f2_idx",
                    ),
                ],
            },
        ),
    ]
