# Generated by Django 5.1.7 on 2025-06-19 10:50

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dispute", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AdminTeamMember",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("ADMIN", "ADMIN"),
                            ("CUSTOMER_SUPPORT", "CUSTOMER_SUPPORT"),
                            ("OPERATIONS", "OPERATIONS"),
                            ("RECONCILIATION", "RECONCILIATION"),
                            ("DEVELOPER", "DEVELOPER"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("is_active", models.BooleanField(db_index=True, default=True)),
                (
                    "added_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="added_admin_members",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="admin_team_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DisputeResponse",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "message",
                    models.TextField(help_text="Response message from admin team"),
                ),
                (
                    "previous_status",
                    models.CharField(
                        choices=[
                            ("PENDING", "PENDING"),
                            ("IN_REVIEW", "IN_REVIEW"),
                            ("RESOLVED", "RESOLVED"),
                        ],
                        help_text="Status before this response",
                        max_length=20,
                    ),
                ),
                (
                    "new_status",
                    models.CharField(
                        choices=[
                            ("PENDING", "PENDING"),
                            ("IN_REVIEW", "IN_REVIEW"),
                            ("RESOLVED", "RESOLVED"),
                        ],
                        help_text="Status after this response",
                        max_length=20,
                    ),
                ),
                (
                    "is_internal_note",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this is an internal note (not visible to merchant)",
                    ),
                ),
                (
                    "dispute",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="responses",
                        to="dispute.dispute",
                    ),
                ),
                (
                    "responder",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dispute_responses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DisputeAssignment",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(db_index=True, default=True)),
                ("notes", models.TextField(blank=True, null=True)),
                (
                    "assigned_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dispute_assignments_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assigned_disputes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "dispute",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assignments",
                        to="dispute.dispute",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["dispute", "is_active"],
                        name="dispute_dis_dispute_5d244e_idx",
                    ),
                    models.Index(
                        fields=["assigned_to", "is_active"],
                        name="dispute_dis_assigne_60af33_idx",
                    ),
                ],
            },
        ),
    ]
