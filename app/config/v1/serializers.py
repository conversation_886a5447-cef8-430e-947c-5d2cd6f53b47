from common.enums import ProductEnum, ProviderEnum, VenderEnum
from config.models import DefaultVender, ProviderSettings
from django.core.cache import cache
from django.db import transaction
from rest_framework import serializers


class DefaultProviderSerializer(serializers.ModelSerializer):

    product = serializers.ChoiceField(choices=ProductEnum.choices())
    provider = serializers.ChoiceField(choices=ProviderEnum.choices())
    vender = serializers.ChoiceField(choices=VenderEnum.choices())

    class Meta:
        model = DefaultVender
        fields = "__all__"

    def validate(self, attrs):
        attrs = super().validate(attrs)

        exists = ProviderSettings.objects.filter(
            product=attrs["product"],
            provider=attrs["provider"],
            vender=attrs["vender"],
            is_active=True,
        ).exists()

        if not exists:
            raise serializers.ValidationError(
                {
                    "provider": (
                        f"No active configuration found for "
                        f"{attrs['provider']} / {attrs['product']} / {attrs['vender']}"
                    )
                }
            )
        return attrs

    @transaction.atomic
    def create(self, validated_data):
        instance, _ = DefaultVender.objects.update_or_create(
            product=validated_data["product"],
            provider=validated_data["provider"],
            defaults={"vender": validated_data["vender"]},
        )
        cache_key = (
            f"default_provider:{validated_data['product']}:{validated_data['provider']}"
        )
        cache.delete(cache_key)
        return instance
