from common.dtos import CoreServiceResponse
from common.enums import CoreServiceResponseStatus
from config.v1.serializers import DefaultProviderSerializer
from drf_spectacular.utils import extend_schema_view
from pykolofinance.common.schema import header
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response


@extend_schema_view(
    set_vender=header,
)
class VenderViewSet(viewsets.GenericViewSet):
    http_method_names = ["post", "get"]

    @action(
        methods=["POST"],
        serializer_class=DefaultProviderSerializer,
        detail=False,
        url_path="set-vender",
    )
    def set_vender(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        response = CoreServiceResponse(
            success=True,
            status=CoreServiceResponseStatus.Success.value,
        )
        return Response(response.to_dict(), status=status.HTTP_200_OK)
