# Generated by Django 5.1.7 on 2025-06-20 11:14

import common.kgs
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("config", "0002_alter_bank_options"),
    ]

    operations = [
        migrations.CreateModel(
            name="DefaultVender",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "product",
                    models.CharField(
                        choices=[
                            ("Transfer", "Transfer"),
                            ("Airtime", "Airtime"),
                            ("Data", "Data"),
                            ("Kyc", "Kyc"),
                            ("VirtualAccount", "VirtualAccount"),
                            ("RecurringDebit", "RecurringDebit"),
                            ("CableTv", "CableTv"),
                            ("Epin", "Epin"),
                            ("Education", "Education"),
                            ("Electricity", "Electricity"),
                            ("Betting", "Betting"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("Mtn", "Mtn"),
                            ("Glo", "Glo"),
                            ("Airtel", "Airtel"),
                            ("9Mobile", "9Mobile"),
                            ("Dstv", "Dstv"),
                            ("Gotv", "Gotv"),
                            ("Startimes", "Startimes"),
                            ("AbujaElectric", "AbujaElectric"),
                            ("BeninElectric", "BeninElectric"),
                            ("EnuguElectric", "EnuguElectric"),
                            ("EkoElectric", "EkoElectric"),
                            ("IbadanElectric", "IbadanElectric"),
                            ("IkejaElectric", "IkejaElectric"),
                            ("JosElectric", "JosElectric"),
                            ("PortharcourtElectric", "PortharcourtElectric"),
                            ("KadunaElectric", "KadunaElectric"),
                            ("KanoElectric", "KanoElectric"),
                            ("YolaElectric", "YolaElectric"),
                            ("Bet9ja", "Bet9ja"),
                            ("BangBet", "BangBet"),
                            ("SupaBet", "SupaBet"),
                            ("CloudBet", "CloudBet"),
                            ("BetLion", "BetLion"),
                            ("1xBet", "1xBet"),
                            ("MerryBet", "MerryBet"),
                            ("BetWay", "BetWay"),
                            ("BetLand", "BetLand"),
                            ("BetKing", "BetKing"),
                            ("LiveScoreBet", "LiveScoreBet"),
                            ("NaijaBet", "NaijaBet"),
                            ("Nin", "Nin"),
                            ("Bvn", "Bvn"),
                            ("PhoneNumberLookup", "PhoneNumberLookup"),
                            ("Waec", "Waec"),
                            ("Jamb", "Jamb"),
                            ("Transfer", "Transfer"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "vender",
                    models.CharField(
                        choices=[
                            ("Shago", "Shago"),
                            ("Sonite", "Sonite"),
                            ("Quickteller", "Quickteller"),
                            ("Glo", "Glo"),
                            ("YouVerify", "YouVerify"),
                            ("Blusalt", "Blusalt"),
                            ("Access", "Access"),
                            ("Jamb", "Jamb"),
                            ("EasyPayWema", "EasyPayWema"),
                            ("EasyPayZenith", "EasyPayZenith"),
                            ("EasyPayFidelity", "EasyPayFidelity"),
                            ("EasyPayEcobank", "EasyPayEcobank"),
                            ("MamaAfrica", "MamaAfrica"),
                            ("Dojah", "Dojah"),
                            ("Wema", "Wema"),
                            ("Kolomoni", "Kolomoni"),
                        ],
                        max_length=50,
                    ),
                ),
            ],
            options={
                "ordering": ("vender",),
            },
        ),
        migrations.CreateModel(
            name="ProviderSettings",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "product",
                    models.CharField(
                        choices=[
                            ("Transfer", "Transfer"),
                            ("Airtime", "Airtime"),
                            ("Data", "Data"),
                            ("Kyc", "Kyc"),
                            ("VirtualAccount", "VirtualAccount"),
                            ("RecurringDebit", "RecurringDebit"),
                            ("CableTv", "CableTv"),
                            ("Epin", "Epin"),
                            ("Education", "Education"),
                            ("Electricity", "Electricity"),
                            ("Betting", "Betting"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("Mtn", "Mtn"),
                            ("Glo", "Glo"),
                            ("Airtel", "Airtel"),
                            ("9Mobile", "9Mobile"),
                            ("Dstv", "Dstv"),
                            ("Gotv", "Gotv"),
                            ("Startimes", "Startimes"),
                            ("AbujaElectric", "AbujaElectric"),
                            ("BeninElectric", "BeninElectric"),
                            ("EnuguElectric", "EnuguElectric"),
                            ("EkoElectric", "EkoElectric"),
                            ("IbadanElectric", "IbadanElectric"),
                            ("IkejaElectric", "IkejaElectric"),
                            ("JosElectric", "JosElectric"),
                            ("PortharcourtElectric", "PortharcourtElectric"),
                            ("KadunaElectric", "KadunaElectric"),
                            ("KanoElectric", "KanoElectric"),
                            ("YolaElectric", "YolaElectric"),
                            ("Bet9ja", "Bet9ja"),
                            ("BangBet", "BangBet"),
                            ("SupaBet", "SupaBet"),
                            ("CloudBet", "CloudBet"),
                            ("BetLion", "BetLion"),
                            ("1xBet", "1xBet"),
                            ("MerryBet", "MerryBet"),
                            ("BetWay", "BetWay"),
                            ("BetLand", "BetLand"),
                            ("BetKing", "BetKing"),
                            ("LiveScoreBet", "LiveScoreBet"),
                            ("NaijaBet", "NaijaBet"),
                            ("Nin", "Nin"),
                            ("Bvn", "Bvn"),
                            ("PhoneNumberLookup", "PhoneNumberLookup"),
                            ("Waec", "Waec"),
                            ("Jamb", "Jamb"),
                            ("Transfer", "Transfer"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "vender",
                    models.CharField(
                        choices=[
                            ("Shago", "Shago"),
                            ("Sonite", "Sonite"),
                            ("Quickteller", "Quickteller"),
                            ("Glo", "Glo"),
                            ("YouVerify", "YouVerify"),
                            ("Blusalt", "Blusalt"),
                            ("Access", "Access"),
                            ("Jamb", "Jamb"),
                            ("EasyPayWema", "EasyPayWema"),
                            ("EasyPayZenith", "EasyPayZenith"),
                            ("EasyPayFidelity", "EasyPayFidelity"),
                            ("EasyPayEcobank", "EasyPayEcobank"),
                            ("MamaAfrica", "MamaAfrica"),
                            ("Dojah", "Dojah"),
                            ("Wema", "Wema"),
                            ("Kolomoni", "Kolomoni"),
                        ],
                        max_length=50,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
            ],
            options={
                "ordering": ("vender",),
                "unique_together": {("product", "vender", "provider")},
            },
        ),
    ]
