# Generated by Django 5.1.7 on 2025-06-19 20:20

import common.kgs
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Bank",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(db_index=True, max_length=100)),
                (
                    "bank_code",
                    models.CharField(
                        blank=True, db_index=True, max_length=6, null=True
                    ),
                ),
                (
                    "institution_code",
                    models.CharField(
                        blank=True, db_index=True, max_length=6, null=True
                    ),
                ),
            ],
            options={
                "ordering": ("-name",),
            },
        ),
    ]
