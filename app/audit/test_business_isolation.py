#!/usr/bin/env python3
"""
Test script to verify business isolation in audit logging system

This script demonstrates that:
1. Businesses can only see their own logs
2. Admins can see logs from all businesses
3. Business team members can see logs from their business
4. Proper filtering and access controls are in place
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from business.models import Business
from audit.models import AuditLog
from audit.views import AuditLogViewSet, BusinessOwnerAuditLogViewSet
from audit.utils import log_login_attempt, log_api_key_generation, log_dispute_creation

User = get_user_model()


class BusinessIsolationTester:
    """Test business isolation in audit logging"""
    
    def __init__(self):
        self.factory = RequestFactory()
        self.setup_test_data()
    
    def setup_test_data(self):
        """Create test users and businesses"""
        print("🔧 Setting up test data...")
        
        # Create Business 1 and its owner
        self.business1_owner, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'firstname': 'Business1',
                'lastname': 'Owner',
                'role': 'Business_Owner',
                'is_active': True,
                'verified': True,
            }
        )
        
        self.business1, created = Business.objects.get_or_create(
            owner=self.business1_owner,
            defaults={
                'name': 'Business One',
                'email': '<EMAIL>',
                'status': 'Active',
            }
        )
        
        # Create Business 2 and its owner
        self.business2_owner, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'firstname': 'Business2',
                'lastname': 'Owner',
                'role': 'Business_Owner',
                'is_active': True,
                'verified': True,
            }
        )
        
        self.business2, created = Business.objects.get_or_create(
            owner=self.business2_owner,
            defaults={
                'name': 'Business Two',
                'email': '<EMAIL>',
                'status': 'Active',
            }
        )
        
        # Create Admin user
        self.admin_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'firstname': 'System',
                'lastname': 'Admin',
                'role': 'Admin',
                'is_active': True,
                'verified': True,
                'is_staff': True,
            }
        )
        
        print("✓ Test data setup completed")
    
    def create_sample_audit_logs(self):
        """Create sample audit logs for testing"""
        print("\n📝 Creating sample audit logs...")
        
        # Create logs for Business 1
        request1 = self.factory.post('/api/test/')
        request1.user = self.business1_owner
        request1.META['REMOTE_ADDR'] = '192.168.1.1'
        request1.META['HTTP_USER_AGENT'] = 'Business1 Browser'
        
        log_login_attempt(request1, user=self.business1_owner, success=True)
        log_api_key_generation(request1, user=self.business1_owner, business=self.business1)
        
        # Create logs for Business 2
        request2 = self.factory.post('/api/test/')
        request2.user = self.business2_owner
        request2.META['REMOTE_ADDR'] = '192.168.1.2'
        request2.META['HTTP_USER_AGENT'] = 'Business2 Browser'
        
        log_login_attempt(request2, user=self.business2_owner, success=True)
        log_api_key_generation(request2, user=self.business2_owner, business=self.business2)
        
        # Create admin logs
        admin_request = self.factory.post('/admin/test/')
        admin_request.user = self.admin_user
        admin_request.META['REMOTE_ADDR'] = '192.168.1.100'
        admin_request.META['HTTP_USER_AGENT'] = 'Admin Browser'
        
        log_login_attempt(admin_request, user=self.admin_user, success=True)
        
        print("✓ Sample audit logs created")
    
    def test_business1_access(self):
        """Test that Business 1 can only see their own logs"""
        print("\n🏢 Testing Business 1 access...")
        
        # Create request as Business 1 owner
        request = self.factory.get('/api/v1/audit/my-logs/')
        request.user = self.business1_owner
        
        # Test BusinessOwnerAuditLogViewSet
        viewset = BusinessOwnerAuditLogViewSet()
        viewset.request = request
        queryset = viewset.get_queryset()
        
        # Count logs accessible to Business 1
        business1_logs = queryset.count()
        business1_user_logs = queryset.filter(user=self.business1_owner).count()
        business1_business_logs = queryset.filter(
            metadata__business_id=str(self.business1.id)
        ).count()
        
        print(f"  📊 Business 1 can see {business1_logs} total logs")
        print(f"  👤 Business 1 user logs: {business1_user_logs}")
        print(f"  🏢 Business 1 business logs: {business1_business_logs}")
        
        # Verify they can't see Business 2 logs
        business2_logs_visible = queryset.filter(user=self.business2_owner).count()
        print(f"  🚫 Business 2 logs visible to Business 1: {business2_logs_visible}")
        
        assert business2_logs_visible == 0, "Business 1 should not see Business 2 logs!"
        print("  ✅ Business isolation working correctly for Business 1")
        
        return business1_logs
    
    def test_business2_access(self):
        """Test that Business 2 can only see their own logs"""
        print("\n🏢 Testing Business 2 access...")
        
        # Create request as Business 2 owner
        request = self.factory.get('/api/v1/audit/my-logs/')
        request.user = self.business2_owner
        
        # Test BusinessOwnerAuditLogViewSet
        viewset = BusinessOwnerAuditLogViewSet()
        viewset.request = request
        queryset = viewset.get_queryset()
        
        # Count logs accessible to Business 2
        business2_logs = queryset.count()
        business2_user_logs = queryset.filter(user=self.business2_owner).count()
        business2_business_logs = queryset.filter(
            metadata__business_id=str(self.business2.id)
        ).count()
        
        print(f"  📊 Business 2 can see {business2_logs} total logs")
        print(f"  👤 Business 2 user logs: {business2_user_logs}")
        print(f"  🏢 Business 2 business logs: {business2_business_logs}")
        
        # Verify they can't see Business 1 logs
        business1_logs_visible = queryset.filter(user=self.business1_owner).count()
        print(f"  🚫 Business 1 logs visible to Business 2: {business1_logs_visible}")
        
        assert business1_logs_visible == 0, "Business 2 should not see Business 1 logs!"
        print("  ✅ Business isolation working correctly for Business 2")
        
        return business2_logs
    
    def test_admin_access(self):
        """Test that Admin can see all logs from all businesses"""
        print("\n👨‍💼 Testing Admin access...")
        
        # Create request as Admin
        request = self.factory.get('/api/v1/audit/logs/')
        request.user = self.admin_user
        
        # Test AuditLogViewSet (admin endpoint)
        viewset = AuditLogViewSet()
        viewset.request = request
        queryset = viewset.get_queryset()
        
        # Count all logs accessible to admin
        total_logs = queryset.count()
        business1_logs = queryset.filter(user=self.business1_owner).count()
        business2_logs = queryset.filter(user=self.business2_owner).count()
        admin_logs = queryset.filter(user=self.admin_user).count()
        
        print(f"  📊 Admin can see {total_logs} total logs")
        print(f"  🏢 Business 1 logs visible to admin: {business1_logs}")
        print(f"  🏢 Business 2 logs visible to admin: {business2_logs}")
        print(f"  👨‍💼 Admin logs: {admin_logs}")
        
        # Admin should see logs from all businesses
        assert business1_logs > 0, "Admin should see Business 1 logs!"
        assert business2_logs > 0, "Admin should see Business 2 logs!"
        print("  ✅ Admin can access all business logs correctly")
        
        return total_logs
    
    def test_cross_business_isolation(self):
        """Test that businesses cannot access each other's logs via admin endpoint"""
        print("\n🔒 Testing cross-business isolation via admin endpoint...")
        
        # Test Business 1 trying to access admin endpoint
        request = self.factory.get('/api/v1/audit/logs/')
        request.user = self.business1_owner
        
        viewset = AuditLogViewSet()
        viewset.request = request
        queryset = viewset.get_queryset()
        
        # Business user should only see their business logs, not all logs
        business1_via_admin = queryset.count()
        business2_logs_via_admin = queryset.filter(user=self.business2_owner).count()
        
        print(f"  📊 Business 1 via admin endpoint can see {business1_via_admin} logs")
        print(f"  🚫 Business 2 logs visible: {business2_logs_via_admin}")
        
        assert business2_logs_via_admin == 0, "Business 1 should not see Business 2 logs via admin endpoint!"
        print("  ✅ Cross-business isolation working correctly")
    
    def run_all_tests(self):
        """Run all business isolation tests"""
        print("🧪 Starting Business Isolation Tests")
        print("=" * 50)
        
        # Setup and create sample data
        self.create_sample_audit_logs()
        
        # Run tests
        business1_logs = self.test_business1_access()
        business2_logs = self.test_business2_access()
        admin_total_logs = self.test_admin_access()
        self.test_cross_business_isolation()
        
        # Summary
        print("\n" + "=" * 50)
        print("📈 TEST SUMMARY")
        print("=" * 50)
        print(f"✅ Business 1 isolated logs: {business1_logs}")
        print(f"✅ Business 2 isolated logs: {business2_logs}")
        print(f"✅ Admin total access logs: {admin_total_logs}")
        print(f"✅ Cross-business isolation: WORKING")
        print("\n🎉 All business isolation tests PASSED!")
        print("\n🔐 Security Features Verified:")
        print("  • Businesses can only see their own logs")
        print("  • Admins can see logs from all businesses")
        print("  • No cross-business data leakage")
        print("  • Proper access controls in place")


if __name__ == "__main__":
    tester = BusinessIsolationTester()
    tester.run_all_tests()
