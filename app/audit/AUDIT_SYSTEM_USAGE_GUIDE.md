# Audit System Usage Guide

## 🚀 Quick Start

The comprehensive audit logging system is now fully implemented and ready to use. This guide shows you how to test and use the system.

## 🧪 Testing the System

### Run Audit Tests

```bash
# Test all audit events
python manage.py test_audit_logging --test-type=all

# Test only merchant events
python manage.py test_audit_logging --test-type=merchant

# Test only admin events
python manage.py test_audit_logging --test-type=admin
```

### Seed Sample Data with Audit Logs

```bash
# Create sample businesses with audit-enabled operations
python manage.py seed_dispute_data --businesses 2 --disputes 10

# This will create:
# - Business registrations (with audit logs)
# - API key generations (with audit logs)
# - Dispute creations (with audit logs)
# - Team member invitations (with audit logs)
```

## 📊 Viewing Audit Logs

### API Endpoints

#### For Business Owners (Merchant Side)
```bash
# View your own audit logs
GET /api/v1/audit/business-owner/

# Filter by action type
GET /api/v1/audit/business-owner/?action=LOGIN

# Filter by date range
GET /api/v1/audit/business-owner/?created_at__gte=2024-01-01

# Security activity only
GET /api/v1/audit/business-owner/security-activity/
```

#### For Admins
```bash
# View all audit logs
GET /api/v1/audit/

# Filter by user email
GET /api/v1/audit/?email=<EMAIL>

# Filter by business
GET /api/v1/audit/?business_id=123

# Filter by action type
GET /api/v1/audit/?action=DISPUTE_CREATE

# Get statistics
GET /api/v1/audit/statistics/
```

### Sample API Response

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "action": "API_KEY_GENERATE",
      "action_display": "API Key Generated",
      "description": "API private key generated",
      "ip_address": "***********",
      "status": "SUCCESS",
      "resource_type": "APIConfig",
      "metadata": {
        "key_type": "private",
        "business_id": 123,
        "business_name": "Test Business",
        "timestamp": "2024-01-15T10:30:00Z"
      },
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

## 🔧 Adding Custom Audit Logging

### For New Merchant Events

```python
from audit.utils import log_user_action
from audit.models import AuditLog

def my_merchant_action(request):
    # Your business logic here
    
    # Log the action
    log_user_action(
        request=request,
        user=request.user,
        action=AuditLog.CUSTOM_ACTION,  # Define new action if needed
        description="Custom merchant action performed",
        resource_type="CustomResource",
        resource_id="123",
        metadata={
            "business_id": request.user.business.id,
            "custom_field": "value"
        }
    )
```

### For New Admin Events

```python
from audit.utils import log_system_settings_change

def update_system_config(request, setting_name, old_value, new_value):
    # Your admin logic here
    
    # Log the change
    log_system_settings_change(
        request=request,
        user=request.user,
        setting_name=setting_name,
        old_value=old_value,
        new_value=new_value
    )
```

## 🛠️ Middleware Configuration

### Enable Audit Middleware

Add to your `settings.py`:

```python
MIDDLEWARE = [
    # ... other middleware
    'audit.middleware.AuditMiddleware',  # For API call logging
    'audit.middleware.AuditLogAccessMiddleware',  # For audit access logging
    # ... rest of middleware
]
```

### Middleware Configuration

```python
# In settings.py
AUDIT_SETTINGS = {
    'EXCLUDE_PATHS': [
        '/admin/',
        '/static/',
        '/media/',
        '/favicon.ico',
    ],
    'LOG_METHODS': ['POST', 'PUT', 'PATCH', 'DELETE'],
    'ENABLE_API_LOGGING': True,
    'ENABLE_ACCESS_LOGGING': True,
}
```

## 📈 Monitoring & Analytics

### Key Metrics to Monitor

1. **Login Activity**
   - Failed login attempts
   - Admin vs merchant logins
   - Login frequency patterns

2. **API Usage**
   - API call frequency
   - Error rates
   - Most used endpoints

3. **Security Events**
   - Password changes
   - Role updates
   - API key generations

4. **Business Operations**
   - Dispute creation rates
   - Transaction patterns
   - System configuration changes

### Sample Queries

```python
from audit.models import AuditLog
from django.utils import timezone
from datetime import timedelta

# Failed logins in last 24 hours
failed_logins = AuditLog.objects.filter(
    action=AuditLog.LOGIN,
    status=AuditLog.FAILED,
    created_at__gte=timezone.now() - timedelta(days=1)
)

# API key generations by business
api_key_logs = AuditLog.objects.filter(
    action=AuditLog.API_KEY_GENERATE
).values('metadata__business_name').annotate(
    count=Count('id')
)

# Most active users
active_users = AuditLog.objects.values('email').annotate(
    activity_count=Count('id')
).order_by('-activity_count')[:10]
```

## 🔍 Troubleshooting

### Common Issues

1. **Audit logs not appearing**
   - Check middleware is enabled
   - Verify user is authenticated
   - Check excluded paths configuration

2. **Missing business context**
   - Ensure user has business relationship
   - Check business association in user model

3. **Performance issues**
   - Review audit log retention policy
   - Consider async logging for high-volume events
   - Optimize database indexes

### Debug Mode

```python
# Enable debug logging in settings.py
LOGGING = {
    'loggers': {
        'audit': {
            'level': 'DEBUG',
            'handlers': ['console'],
        },
    },
}
```

## 🛡️ Security Considerations

### Data Protection

1. **Sensitive Data**: Audit logs automatically mask sensitive information
2. **Access Control**: Role-based access to audit logs
3. **Retention**: Implement log retention policies
4. **Encryption**: Store audit logs with appropriate encryption

### Compliance

The audit system supports:
- **SOX Compliance**: Complete audit trail
- **GDPR**: Data protection and privacy
- **PCI DSS**: Security monitoring
- **ISO 27001**: Information security management

## 📚 Available Audit Events

### Merchant Events
- `LOGIN` - User login
- `LOGOUT` - User logout  
- `API_ACCESS` - API calls
- `DISPUTE_CREATE` - Dispute creation
- `API_KEY_GENERATE` - API key generation
- `API_KEY_REVOKE` - API key revocation
- `PASSWORD_CHANGE` - Password changes
- `PROFILE_UPDATE` - Profile updates

### Admin Events
- `ADMIN_LOGIN` - Admin login
- `ADMIN_LOGOUT` - Admin logout
- `BUSINESS_ONBOARD` - Merchant onboarding
- `BUSINESS_UPDATE` - Merchant updates
- `ROLE_UPDATE` - User role changes
- `PROVIDER_ADD` - Provider additions
- `PROVIDER_SWITCH` - Provider switching
- `FEE_STRUCTURE_EDIT` - Fee changes
- `TRANSACTION_REVERSE` - Transaction reversals
- `DISPUTE_RESOLVE` - Dispute resolutions
- `WALLET_FUNDING` - Wallet funding
- `SYSTEM_CONFIG` - System settings
- `AUDIT_LOG_ACCESS` - Audit access
- `TEAM_MEMBER_INVITE` - Team invitations

## 🎉 Success!

Your comprehensive audit logging system is now fully operational and capturing all required events for both merchants and admins. The system provides:

✅ **Complete Coverage** - All requested events are captured
✅ **Business Context** - Proper filtering and association
✅ **Performance Optimized** - Efficient logging with minimal impact
✅ **Security Focused** - Proper access controls and data protection
✅ **Compliance Ready** - Meets audit and regulatory requirements

For additional support or custom audit requirements, refer to the implementation documentation or extend the existing utility functions.
