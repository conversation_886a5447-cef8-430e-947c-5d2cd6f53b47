"""
Audit middleware for capturing API calls and other audit events
"""
import json
import logging
from django.utils.deprecation import MiddlewareMixin
from django.urls import resolve, Resolver404
from .utils import log_user_action
from .models import AuditLog

logger = logging.getLogger(__name__)


class AuditMiddleware(MiddlewareMixin):
    """
    Middleware to automatically capture API calls for audit logging
    """
    
    # Endpoints to exclude from API call logging (to avoid noise)
    EXCLUDED_PATHS = [
        '/admin/',
        '/static/',
        '/media/',
        '/favicon.ico',
        '/api/v1/audit/',  # Avoid logging audit log access here (handled separately)
        '/api/docs/',
        '/api/schema/',
    ]
    
    # Methods to log
    LOGGED_METHODS = ['POST', 'PUT', 'PATCH', 'DELETE']
    
    def process_response(self, request, response):
        """
        Log API calls after response is generated
        """
        try:
            # Skip if path should be excluded
            if any(request.path.startswith(excluded) for excluded in self.EXCLUDED_PATHS):
                return response
            
            # Only log specific HTTP methods
            if request.method not in self.LOGGED_METHODS:
                return response
            
            # Only log if user is authenticated
            if not hasattr(request, 'user') or not request.user.is_authenticated:
                return response
            
            # Get URL info
            try:
                resolver_match = resolve(request.path_info)
                view_name = resolver_match.view_name
                namespace = resolver_match.namespace
            except Resolver404:
                view_name = None
                namespace = None
            
            # Skip admin namespace
            if namespace == 'admin':
                return response
            
            # Determine if this is a business/merchant API call
            is_merchant_api = self._is_merchant_api_call(request, namespace, view_name)
            
            if is_merchant_api:
                self._log_api_call(request, response, view_name, namespace)
                
        except Exception as e:
            # Don't let audit logging break the application
            logger.error(f"Error in audit middleware: {str(e)}")
        
        return response
    
    def _is_merchant_api_call(self, request, namespace, view_name):
        """
        Determine if this is a merchant/business API call that should be audited
        """
        # Check if user has a business (is a merchant)
        if hasattr(request.user, 'business') and request.user.business:
            return True
        
        # Check if this is a business-related API endpoint
        business_namespaces = ['business', 'dispute', 'vas', 'wallet', 'transaction']
        if namespace in business_namespaces:
            return True
        
        # Check for business-related view names
        business_views = ['business', 'dispute', 'transaction', 'wallet', 'vas']
        if view_name and any(bv in view_name.lower() for bv in business_views):
            return True
        
        return False
    
    def _log_api_call(self, request, response, view_name, namespace):
        """
        Log the API call
        """
        try:
            # Prepare description
            action_description = f"{request.method} {request.path}"
            if view_name:
                action_description += f" ({view_name})"
            
            # Prepare metadata
            metadata = {
                "method": request.method,
                "path": request.path,
                "view_name": view_name,
                "namespace": namespace,
                "status_code": response.status_code,
                "content_type": response.get('Content-Type', ''),
            }
            
            # Add business context if available
            if hasattr(request.user, 'business') and request.user.business:
                metadata["business_id"] = request.user.business.id
                metadata["business_name"] = request.user.business.name
            
            # Determine status based on response code
            if 200 <= response.status_code < 300:
                status = AuditLog.SUCCESS
            elif 400 <= response.status_code < 500:
                status = AuditLog.FAILED
            else:
                status = AuditLog.FAILED
            
            # Log the API call
            log_user_action(
                request=request,
                user=request.user,
                action=AuditLog.API_ACCESS,
                description=action_description,
                status=status,
                resource_type="API",
                resource_id=view_name,
                metadata=metadata,
            )
            
        except Exception as e:
            logger.error(f"Error logging API call: {str(e)}")


class AuditLogAccessMiddleware(MiddlewareMixin):
    """
    Middleware specifically for logging audit log access
    """
    
    def process_response(self, request, response):
        """
        Log audit log access
        """
        try:
            # Only log GET requests to audit endpoints
            if request.method != 'GET':
                return response
            
            # Check if this is an audit log access
            if not request.path.startswith('/api/v1/audit/'):
                return response
            
            # Only log if user is authenticated
            if not hasattr(request, 'user') or not request.user.is_authenticated:
                return response
            
            # Only log successful requests
            if response.status_code != 200:
                return response
            
            # Extract filter parameters
            filter_params = dict(request.GET.items()) if request.GET else None
            
            # Determine accessed user email (if filtering by specific user)
            accessed_user_email = filter_params.get('email') if filter_params else None
            
            # Import here to avoid circular imports
            from .utils import log_audit_log_access
            
            log_audit_log_access(
                request=request,
                user=request.user,
                accessed_user_email=accessed_user_email,
                filter_params=filter_params,
            )
            
        except Exception as e:
            logger.error(f"Error in audit log access middleware: {str(e)}")
        
        return response
