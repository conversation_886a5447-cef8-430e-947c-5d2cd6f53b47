"""
Management command to test the comprehensive audit logging system
"""
from django.core.management.base import BaseCommand
from django.test import RequestFactory
from django.contrib.auth import get_user_model
from business.models import Business
from audit.models import AuditLog
from audit.utils import *

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the comprehensive audit logging system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-type',
            type=str,
            choices=['merchant', 'admin', 'all'],
            default='all',
            help='Type of audit tests to run'
        )

    def handle(self, *args, **options):
        test_type = options['test_type']
        
        self.stdout.write(
            self.style.SUCCESS(f'🧪 Testing Audit Logging System - {test_type.upper()}')
        )
        
        # Create test request factory
        self.factory = RequestFactory()
        
        # Get or create test users
        self.setup_test_users()
        
        if test_type in ['merchant', 'all']:
            self.test_merchant_events()
        
        if test_type in ['admin', 'all']:
            self.test_admin_events()
        
        # Show results
        self.show_results()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Audit logging tests completed successfully!')
        )

    def setup_test_users(self):
        """Setup test users for audit logging"""
        # Create business owner
        self.business_owner, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'firstname': 'Test',
                'lastname': 'Business',
                'role': 'Business_Owner',
                'is_active': True,
                'verified': True,
            }
        )
        
        # Create business
        self.business, created = Business.objects.get_or_create(
            owner=self.business_owner,
            defaults={
                'name': 'Test Business',
                'email': '<EMAIL>',
                'status': 'Active',
            }
        )
        
        # Create admin user
        self.admin_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'firstname': 'Test',
                'lastname': 'Admin',
                'role': 'Admin',
                'is_active': True,
                'verified': True,
            }
        )
        
        self.stdout.write('✓ Test users setup completed')

    def test_merchant_events(self):
        """Test merchant-side audit events"""
        self.stdout.write(self.style.WARNING('\n📊 Testing Merchant Events...'))
        
        # Create mock request
        request = self.factory.post('/api/test/')
        request.user = self.business_owner
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        request.META['HTTP_USER_AGENT'] = 'Test Browser'
        
        # Test 1: Login
        log_login_attempt(request, user=self.business_owner, success=True)
        self.stdout.write('  ✓ Login event logged')
        
        # Test 2: API Key Generation
        log_api_key_generation(
            request=request,
            user=self.business_owner,
            business=self.business,
            key_type="private"
        )
        self.stdout.write('  ✓ API Key Generation logged')
        
        # Test 3: Profile Update
        log_profile_update(
            request=request,
            user=self.business_owner,
            old_data={'firstname': 'Old', 'lastname': 'Name'},
            new_data={'firstname': 'Test', 'lastname': 'Business'}
        )
        self.stdout.write('  ✓ Profile Update logged')
        
        # Test 4: Team Member Invitation
        log_team_member_invitation(
            request=request,
            user=self.business_owner,
            invited_email='<EMAIL>',
            role='Merchant_Admin',
            business=self.business
        )
        self.stdout.write('  ✓ Team Member Invitation logged')
        
        # Test 5: Logout
        log_logout(request=request, user=self.business_owner)
        self.stdout.write('  ✓ Logout event logged')

    def test_admin_events(self):
        """Test admin-side audit events"""
        self.stdout.write(self.style.WARNING('\n🔧 Testing Admin Events...'))
        
        # Create mock request
        request = self.factory.post('/admin/test/')
        request.user = self.admin_user
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        request.META['HTTP_USER_AGENT'] = 'Admin Browser'
        
        # Test 1: Admin Login
        log_admin_login(request, user=self.admin_user, success=True)
        self.stdout.write('  ✓ Admin Login logged')
        
        # Test 2: Merchant Onboarding
        log_merchant_onboarding(
            request=request,
            user=self.admin_user,
            business=self.business,
            onboarded_by=self.admin_user
        )
        self.stdout.write('  ✓ Merchant Onboarding logged')
        
        # Test 3: Role Update
        log_role_update(
            request=request,
            user=self.admin_user,
            target_user=self.business_owner,
            old_role='User',
            new_role='Business_Owner'
        )
        self.stdout.write('  ✓ Role Update logged')
        
        # Test 4: Provider Addition
        log_provider_addition(
            request=request,
            user=self.admin_user,
            provider_name='New VAS Provider',
            provider_type='Airtime'
        )
        self.stdout.write('  ✓ Provider Addition logged')
        
        # Test 5: Fee Structure Edit
        log_fee_structure_edit(
            request=request,
            user=self.admin_user,
            fee_type='airtime_fee',
            old_values={'percentage': 2.5},
            new_values={'percentage': 3.0}
        )
        self.stdout.write('  ✓ Fee Structure Edit logged')
        
        # Test 6: System Settings Change
        log_system_settings_change(
            request=request,
            user=self.admin_user,
            setting_name='max_transaction_limit',
            old_value='10000',
            new_value='50000'
        )
        self.stdout.write('  ✓ System Settings Change logged')
        
        # Test 7: Audit Log Access
        log_audit_log_access(
            request=request,
            user=self.admin_user,
            accessed_user_email='<EMAIL>',
            filter_params={'action': 'LOGIN', 'status': 'SUCCESS'}
        )
        self.stdout.write('  ✓ Audit Log Access logged')
        
        # Test 8: Admin Logout
        log_admin_logout(request=request, user=self.admin_user)
        self.stdout.write('  ✓ Admin Logout logged')

    def show_results(self):
        """Show audit logging results"""
        self.stdout.write(self.style.WARNING('\n📈 Audit Logging Results:'))
        
        # Count total audit logs
        total_logs = AuditLog.objects.count()
        self.stdout.write(f'  📊 Total Audit Logs: {total_logs}')
        
        # Count by action type
        action_counts = {}
        for log in AuditLog.objects.all():
            action = log.get_action_display()
            action_counts[action] = action_counts.get(action, 0) + 1
        
        self.stdout.write('\n  📋 Logs by Action Type:')
        for action, count in sorted(action_counts.items()):
            self.stdout.write(f'    • {action}: {count}')
        
        # Show recent logs
        recent_logs = AuditLog.objects.order_by('-created_at')[:5]
        self.stdout.write('\n  🕒 Recent Audit Logs:')
        for log in recent_logs:
            self.stdout.write(
                f'    • {log.created_at.strftime("%H:%M:%S")} - '
                f'{log.email} - {log.get_action_display()}'
            )
        
        # Business-specific logs
        business_logs = AuditLog.objects.filter(user=self.business_owner).count()
        admin_logs = AuditLog.objects.filter(user=self.admin_user).count()
        
        self.stdout.write(f'\n  🏢 Business Owner Logs: {business_logs}')
        self.stdout.write(f'  👨‍💼 Admin Logs: {admin_logs}')
        
        self.stdout.write(
            self.style.SUCCESS(f'\n✨ All audit events are being properly captured!')
        )
